# a: 1.007, b: 1.174, c: 0.650

"""
Enhanced Modal Analysis with Sobol Sampling and Dynamic Load Switching

This script performs comprehensive power system modal analysis using:
- Sobol sequence sampling for efficient parameter space exploration
- Dynamic load switching patterns for robustness analysis
- PowerFactory integration for detailed system modeling
- Comprehensive data collection (generators, buses, lines, eigenvalues)

The output maintains compatibility with reference data structure (78 columns).
"""

# Standard library imports
import pandas as pd  # Data manipulation and CSV handling
import time  # Time measurement for performance monitoring
import sys  # System-specific parameters and functions
import os  # Operating system interface
import traceback  # Exception handling and debugging
import random  # Random number generation for switching patterns
import numpy as np  # Numerical computing
from datetime import datetime  # Date and time handling

# Scientific computing imports
from scipy.stats import qmc  # Quasi-Monte Carlo sampling (Sobol sequences)


def save_console_output(file_path, func, *args, **kwargs):
    """
    Wrapper function to redirect console output to both terminal and file

    This function ensures all print statements are captured in both console
    and a log file for later analysis and debugging.

    Args:
        file_path: Path to output log file
        func: Function to execute with output capturing
        *args, **kwargs: Arguments to pass to the function
    """
    with open(file_path, 'w') as f:
        try:
            # Pass the file handle to the function
            func(f, *args, **kwargs)
        except Exception as e:
            error_msg = f"Error: {str(e)}"
            print(error_msg)  # Console
            print(error_msg, file=f)  # File
            traceback.print_exc()  # Console
            traceback.print_exc(file=f)  # File


def generate_sobol_samples(n_samples, n_dimensions=3, bounds=(0.5, 1.2)):
    """
    Generate Sobol sequence samples for load scaling factors

    Sobol sequences provide better space-filling properties compared to
    pseudo-random sampling, ensuring more uniform coverage of the parameter space.
    This is particularly important for sensitivity analysis and system exploration.

    Args:
        n_samples: Number of samples to generate
        n_dimensions: Number of dimensions (3 for a, b, c load scaling factors)
        bounds: Tuple of (min, max) values for scaling factors
                Default (0.5, 1.2) represents 50% to 120% load scaling

    Returns:
        Array of shape (n_samples, n_dimensions) with Sobol samples
        Each row represents [load_a_scale, load_b_scale, load_c_scale]
    """
    sampler = qmc.Sobol(d=n_dimensions, scramble=True)
    samples = sampler.random(n_samples)
    # Scale from [0,1] to [bounds[0], bounds[1]]
    scaled_samples = qmc.scale(samples, bounds[0], bounds[1])
    return scaled_samples


def get_load_switches(app):
    """
    Identify and retrieve switches connected to the three main loads

    This function searches for switches associated with Load A(1), Load B(1),
    and Load C(1) by name matching. If direct name matching fails, it falls
    back to using the first three available switches in the system.

    Args:
        app: PowerFactory application object

    Returns:
        List of dictionaries containing switch information:
        [{'switch': switch_obj, 'load_name': str, 'name': str}, ...]
    """
    switches = []
    all_switches = app.GetCalcRelevantObjects('*.ElmCoup')

    # Filter switches connected to loads A(1), B(1), and C(1)
    load_names = ['Load A(1)', 'Load B(1)', 'Load C(1)']
    for switch in all_switches:
        switch_name = switch.loc_name
        for load_name in load_names:
            if load_name.replace(' ', '_').replace('(1)', '') in switch_name or \
                    load_name.replace(' ', '').replace('(1)', '') in switch_name:
                switches.append({
                    'switch': switch,
                    'load_name': load_name,
                    'name': switch_name
                })

    # If we couldn't find switches by name matching, get first 3 switches as fallback
    if len(switches) < 3:
        switches = []
        for i, switch in enumerate(all_switches[:3]):
            switches.append({
                'switch': switch,
                'load_name': load_names[i] if i < len(load_names) else f'Load_{i}',
                'name': switch.loc_name
            })

    return switches


def toggle_switch(switch_obj, state):
    """
    Control switch state (open/closed) in PowerFactory

    Args:
        switch_obj: PowerFactory switch object (ElmCoup)
        state: Boolean - True for closed (on), False for open (off)
    """
    if switch_obj:
        switch_obj.on_off = 1 if state else 0  # 1 = closed/on, 0 = open/off


def generate_switching_pattern():
    """
    Generate random switching pattern for the three main loads

    Creates a random combination of load connection states to simulate
    various operational scenarios and test system robustness.

    Returns:
        Dictionary with random states for each load:
        {'Load A(1)': bool, 'Load B(1)': bool, 'Load C(1)': bool}
    """
    return {
        'Load A(1)': random.choice([True, False]),
        'Load B(1)': random.choice([True, False]),
        'Load C(1)': random.choice([True, False])
    }


def apply_load_scaling(app, scaling_factors):
    """
    Apply Sobol-generated scaling factors to system loads

    This function modifies the load scaling parameters in PowerFactory
    based on the generated Sobol samples. It handles both naming conventions
    found in different PowerFactory models.

    Args:
        app: PowerFactory application object
        scaling_factors: Dictionary with scaling factors
                        {'a': float, 'b': float, 'c': float}

    Note:
        - Uses scale0 attribute to modify load magnitudes
        - Applies 0.01 multiplier for loads with "(1)" suffix
        - Direct scaling for loads without suffix
    """
    loads = app.GetCalcRelevantObjects('*.ElmLod')

    for load in loads:
        load_name = load.loc_name
        if load_name == 'Load A(1)':
            load.scale0 = scaling_factors['a'] * 0.01
        elif load_name == 'Load B(1)':
            load.scale0 = scaling_factors['b'] * 0.01
        elif load_name == 'Load C(1)':
            load.scale0 = scaling_factors['c'] * 0.01
        elif load_name == 'Load A':
            load.scale0 = scaling_factors['a']
        elif load_name == 'Load B':
            load.scale0 = scaling_factors['b']
        elif load_name == 'Load C':
            load.scale0 = scaling_factors['c']


def apply_switching_states(switches, switch_states):
    """
    Apply the generated switching pattern to physical switches

    Args:
        switches: List of switch information dictionaries from get_load_switches()
        switch_states: Dictionary of load states from generate_switching_pattern()
    """
    for switch_info in switches:
        switch_obj = switch_info['switch']
        load_name = switch_info['load_name']
        if load_name in switch_states:
            toggle_switch(switch_obj, switch_states[load_name])


def collect_modal_data(app, sample_time, scaling_factors, switch_states):
    """
    Collect comprehensive system data including generators, buses, lines, and modal analysis

    This is the core data collection function that gathers all system measurements
    and performs modal analysis to extract eigenvalues. The output structure exactly
    matches the reference format with 78 columns.

    Column Structure (78 total):
    - Time (1 column)
    - Generator data: G2, G3, G1 × 3 parameters each = 9 columns (P, Q, speed)
    - Bus voltage data: 9 buses × 2 parameters each = 18 columns (Vm, V_angle)
    - Line current data: 6 lines × 2 parameters each = 12 columns (Im, I_angle)
    - Eigenvalues: 19 eigenvalues × 2 components each = 38 columns (real, imaginary)

    Args:
        app: PowerFactory application object
        sample_time: Current simulation time in seconds
        scaling_factors: Dictionary with current load scaling factors
        switch_states: Dictionary with current switch positions

    Returns:
        Dictionary representing one row of data with exactly 78 columns
    """
    # Initialize row with Time column first (matching reference structure)
    row = {'Time': sample_time}

    # Get system objects in the exact order as reference
    generators = app.GetCalcRelevantObjects('*.ElmSym')
    buses = app.GetCalcRelevantObjects('*.ElmTerm')
    lines = app.GetCalcRelevantObjects('*.ElmLne')

    # Collect generator data in exact order: G2, G3, G1 (matching reference)
    # Reference order: G2 P, G2 Q, G2 speed, G3 P, G3 Q, G3 speed, G1 P, G1 Q, G1 speed
    generator_order = ['G2', 'G3', 'G1']
    for gen_name in generator_order:
        generator = None
        # Find generator by name
        for gen in generators:
            if gen.loc_name == gen_name:
                generator = gen
                break

        if generator:
            try:
                # Extract real power, reactive power, and rotor speed
                gP = generator.GetAttribute('s:P1')  # Active power (MW)
                gQ = generator.GetAttribute('s:Q1')  # Reactive power (MVAr)
                speed = generator.GetAttribute('s:speed')  # Rotor speed (pu)

                # Store with proper default values if None
                row[f'{gen_name} P'] = gP if gP is not None else 0.0
                row[f'{gen_name} Q'] = gQ if gQ is not None else 0.0
                row[f'{gen_name} speed'] = speed if speed is not None else 1.0
            except Exception as e:
                print(f"Error reading generator {gen_name}: {e}")
                # Use default values on error
                row[f'{gen_name} P'] = 0.0
                row[f'{gen_name} Q'] = 0.0
                row[f'{gen_name} speed'] = 1.0
        else:
            # Generator not found - use default values
            row[f'{gen_name} P'] = 0.0
            row[f'{gen_name} Q'] = 0.0
            row[f'{gen_name} speed'] = 1.0

    # Collect bus voltage data in exact order: Bus 1, Bus 2, Bus 8, Bus 9, Bus 3, Bus 6, Bus 4, Bus 5, Bus 7
    # Reference order: Bus X Vm, Bus X V_angle for each bus in sequence
    bus_order = ['Bus 1', 'Bus 2', 'Bus 8', 'Bus 9', 'Bus 3', 'Bus 6', 'Bus 4', 'Bus 5', 'Bus 7']
    for bus_name in bus_order:
        bus = None
        # Find bus by name
        for b in buses:
            if b.loc_name == bus_name:
                bus = b
                break

        if bus:
            try:
                # Extract voltage magnitude and angle
                vm = bus.GetAttribute('m:u1')  # Voltage magnitude (pu)
                va = bus.GetAttribute('m:phiu')  # Voltage angle (degrees)

                # Store with proper default values if None
                row[f'{bus_name} Vm'] = vm if vm is not None else 0.0
                row[f'{bus_name} V_angle'] = va if va is not None else 0.0
            except Exception as e:
                print(f"Error reading bus {bus_name}: {e}")
                # Use default values on error
                row[f'{bus_name} Vm'] = 0.0
                row[f'{bus_name} V_angle'] = 0.0
        else:
            # Bus not found - use default values
            row[f'{bus_name} Vm'] = 0.0
            row[f'{bus_name} V_angle'] = 0.0

    # Collect line current data in exact order: Line 5-7, Line 7-8, Line 8-9, Line 6-9, Line 4-6, Line 4-5
    # Reference order: Line X-Y Im, Line X-Y I_angle for each line in sequence
    line_order = ['Line 5-7', 'Line 7-8', 'Line 8-9', 'Line 6-9', 'Line 4-6', 'Line 4-5']
    for line_name in line_order:
        line = None
        # Find line by name
        for l in lines:
            if l.loc_name == line_name:
                line = l
                break

        if line:
            try:
                # Extract current magnitude and angle at bus1 end
                Im = line.GetAttribute('m:I1:bus1')  # Current magnitude (pu)
                Ia = line.GetAttribute('m:phii:bus1')  # Current angle (degrees)

                # Store with proper default values if None
                row[f'{line_name} Im'] = Im if Im is not None else 0.0
                row[f'{line_name} I_angle'] = Ia if Ia is not None else 0.0
            except Exception as e:
                print(f"Error reading line {line_name}: {e}")
                # Use default values on error
                row[f'{line_name} Im'] = 0.0
                row[f'{line_name} I_angle'] = 0.0
        else:
            # Line not found - use default values
            row[f'{line_name} Im'] = 0.0
            row[f'{line_name} I_angle'] = 0.0

    # Perform modal analysis and collect eigenvalues (EV_0_real to EV_18_imag = 38 columns)
    # Modal analysis provides system stability information through eigenvalue analysis
    modal = app.GetFromStudyCase('ComMod')
    if modal is not None:
        try:
            # Configure modal analysis parameters
            modal.iopt_mod = 1  # Modal analysis mode
            modal.iopt_ev = 1  # Calculate eigenvalues
            modal.iopt_evsel = 0  # Select all eigenvalues
            modal.iopt_evsort = 1  # Sort eigenvalues
            modal.iopt_partfact = 1  # Calculate participation factors
            modal.iopt_evec = 1  # Calculate eigenvectors
            modal.iopt_met = 0  # Method selection
            modal.iopt_noevec = 0  # Include eigenvectors
            modal.iopt_which = 0  # Which eigenvalues to calculate

            # Execute modal analysis
            result = modal.Execute()

            # Read eigenvalues from the output MTL file
            mtl_path = r'C:\Users\<USER>\Downloads\modal_res\EVals.mtl'
            if os.path.exists(mtl_path):
                # Parse eigenvalue file (space-separated format)
                eigenvalues = pd.read_csv(mtl_path, sep=r'\s+', header=None,
                                          names=['Index', 'Group', 'Value1', 'Value2'])

                # Add eigenvalues to row (ensure we have exactly 19 eigenvalues: EV_0 to EV_18)
                for idx in range(19):  # EV_0 to EV_18 (19 eigenvalues total)
                    if idx < len(eigenvalues):
                        # Extract real and imaginary parts
                        real = eigenvalues.iloc[idx]['Value1']
                        imag = eigenvalues.iloc[idx]['Value2']
                    else:
                        # If we have fewer eigenvalues than expected, pad with zeros
                        real = 0.0
                        imag = 0.0

                    # Store eigenvalue components
                    row[f'EV_{idx}_real'] = real
                    row[f'EV_{idx}_imag'] = imag

            else:
                # If no MTL file exists, fill eigenvalues with zeros
                print(f"Warning: MTL file not found at {mtl_path}")
                for idx in range(19):  # EV_0 to EV_18
                    row[f'EV_{idx}_real'] = 0.0
                    row[f'EV_{idx}_imag'] = 0.0

        except Exception as e:
            print(f"Error in modal analysis at time {sample_time:.4f} s: {str(e)}")
            # Fill eigenvalues with zeros on modal analysis error
            for idx in range(19):  # EV_0 to EV_18
                row[f'EV_{idx}_real'] = 0.0
                row[f'EV_{idx}_imag'] = 0.0
    else:
        print("Modal analysis command not available")
        # Fill eigenvalues with zeros if modal analysis unavailable
        for idx in range(19):  # EV_0 to EV_18
            row[f'EV_{idx}_real'] = 0.0
            row[f'EV_{idx}_imag'] = 0.0

    return row


def main(output_file=None):
    """
    Main execution function for enhanced modal analysis with Sobol sampling

    This function orchestrates the entire analysis process:
    1. Sets up PowerFactory environment and loads the system model
    2. Generates Sobol samples for systematic parameter exploration
    3. Runs multiple simulation scenarios with dynamic load switching
    4. Collects comprehensive system data including modal analysis
    5. Saves results to timestamped CSV files for analysis

    Args:
        output_file: Optional file handle for dual output (console + file)
    """

    def dual_print(message):
        """Print to both console and file if file handle provided"""
        print(message)  # Console output
        if output_file:
            print(message, file=output_file)  # File output
            output_file.flush()  # Ensure immediate write

    dual_print("=== Enhanced Modal Analysis with Sobol Sampling and Dynamic Load Switching ===")

    # PowerFactory setup - Configure Python environment for DIgSILENT PowerFactory
    Dig_path = r"D:\\Program Files\\DIgSILENT\\PowerFactory 2024\\Python\\3.12"
    sys.path.append(Dig_path)
    os.environ['PATH'] += ';' + Dig_path

    # Import PowerFactory API and establish connection
    import powerfactory as pf
    app = pf.GetApplication()
    if app is None:
        raise Exception('getting Powerfactory application failed')

    # Define project and study case for the nine-bus system analysis
    projName = 'Nine-bus System'
    study_case = '08- Three Cycles IEEE Type 1 Mag-A_PSS'

    # Activate the PowerFactory project
    project = app.ActivateProject(projName)
    proj = app.GetActiveProject()

    # Navigate to study case folder and activate the specific case
    oFolder_studycase = app.GetProjectFolder('study')
    oCase = oFolder_studycase.GetContents(study_case)[0]
    oCase.Activate()

    # Configure dynamic simulation parameters
    sim = app.GetFromStudyCase('ComSim')
    sim.iopt_sim = 1  # Enable RMS simulation
    sim.tstart = 0.0  # Start time
    sim.tstop = 40.0  # Total simulation duration (40 seconds)
    sim.dtgrd = 1e-5  # Time step for integration (10 microseconds)

    # Get system components for analysis
    buses = app.GetCalcRelevantObjects('*.ElmTerm')  # All terminal/bus objects
    lines = app.GetCalcRelevantObjects('*.ElmLne')  # All transmission line objects
    generators = app.GetCalcRelevantObjects('*.ElmSym')  # All synchronous machine objects

    dual_print(f"Found {len(generators)} generators, {len(buses)} buses, {len(lines)} lines")

    # Create output data directory
    data_folder = 'data'
    if not os.path.exists(data_folder):
        os.makedirs(data_folder)
        dual_print(f"Created data folder: {data_folder}")
    else:
        dual_print(f"Using existing data folder: {data_folder}")

    # Identify and configure load switches for dynamic switching analysis
    switches = get_load_switches(app)
    dual_print(f"Found {len(switches)} load switches:")
    for switch_info in switches:
        dual_print(f"  - {switch_info['name']} for {switch_info['load_name']}")

    # Define simulation parameters for comprehensive analysis
    num_simulations = 2  # Number of different Sobol scenarios to run
    simulation_duration = 40.0  # Duration of each scenario (seconds)
    switching_interval = 2.0  # Frequency of load switching events (seconds)
    sampling_interval = 0.02  # Data collection frequency (50 Hz sampling)

    # Generate Sobol samples for systematic parameter space exploration
    dual_print(f"\nGenerating Sobol samples for {num_simulations} simulations...")
    sobol_samples = generate_sobol_samples(num_simulations, bounds=(0.5, 1.2))  # 50% to 120% load variation

    # Execute multiple simulation scenarios with different Sobol-sampled parameters
    for sim_idx in range(num_simulations):
        dual_print(f"\n=== Running Simulation {sim_idx + 1}/{num_simulations} ===")

        # Extract scaling factors from Sobol sample for this scenario
        scaling_factors = {
            'a': 1.007,  # Load A scaling factor
            'b': 1.174,  # Load B scaling factor
            'c': 0.650  # Load C scaling factor
        }

        dual_print(f"Scaling factors - a: {scaling_factors['a']:.3f}, "
                   f"b: {scaling_factors['b']:.3f}, c: {scaling_factors['c']:.3f}")

        # Apply the Sobol-generated load scaling to the system
        apply_load_scaling(app, scaling_factors)

        # Reset simulation to ensure clean initial conditions
        sim.tstart = 0.0
        sim.tstop = 0.0
        sim.Execute()
        time.sleep(0.0001)  # Brief pause for system stabilization

        # Initialize simulation with short run to establish steady state
        sim.tstop = 0.01
        sim.Execute()
        time.sleep(0.0001)

        # Initialize data collection DataFrame for this scenario
        scenario_data = pd.DataFrame()

        # Initialize simulation control variables
        current_time = 0.0
        next_switch_time = switching_interval
        # Start with all loads connected
        current_switch_states = {'Load A(1)': True, 'Load B(1)': True, 'Load C(1)': True}

        # Apply initial switch configuration
        apply_switching_states(switches, current_switch_states)

        sample_count = 0

        # Main simulation loop with dynamic switching and data collection
        while current_time < simulation_duration:
            # Check if it's time for a load switching event
            if current_time >= next_switch_time:
                # Generate and apply new random switching pattern
                current_switch_states = generate_switching_pattern()
                apply_switching_states(switches, current_switch_states)
                dual_print(f"Switching at {current_time:.2f}s: A={current_switch_states['Load A(1)']}, "
                           f"B={current_switch_states['Load B(1)']}, C={current_switch_states['Load C(1)']}")
                next_switch_time += switching_interval

                # Apply additional random load variations for increased system diversity
                for load in app.GetCalcRelevantObjects('*.ElmLod'):
                    if 'Load' in load.loc_name:
                        variation = 1.0 + random.uniform(-0.005, 0.005)  # ±1.5% random load variation
                        # variation = 1.0
                        # variation = 1.0 + random.choice([-1, 1]) * 0.001  # Random binary value -1 or 1 * 0.01
                        variation = 1.0

                        current_scale = load.scale0 if hasattr(load, 'scale0') else 1.0
                        load.scale0 = current_scale * variation

            # Execute one simulation time step
            sim.tstart = current_time
            sim.tstop = current_time + sampling_interval
            result = sim.Execute()

            # Collect system data at regular intervals
            if sample_count % 1 == 0:  # Collect every sample for maximum data density
                # Allow brief settling time after any system changes
                time.sleep(0.0001)

                # Collect comprehensive system state including modal analysis
                data_row = collect_modal_data(app, current_time, scaling_factors, current_switch_states)
                scenario_data = pd.concat([scenario_data, pd.DataFrame([data_row])], ignore_index=True)

                # Progress reporting every 100 samples
                if len(scenario_data) % 100 == 0:
                    dual_print(f"Collected {len(scenario_data)} samples for scenario {sim_idx + 1}...")

            # Advance simulation time
            current_time += sampling_interval
            sample_count += 1

        # Save scenario data to timestamped CSV file
        timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        scenario_filename = os.path.join(data_folder, f"modal_analysis_scenario_{sim_idx + 1}_{timestamp}.csv")

        scenario_data.to_csv(scenario_filename, index=False)
        dual_print(f"Scenario {sim_idx + 1} saved to: {scenario_filename}")
        dual_print(f"Scenario {sim_idx + 1} samples: {len(scenario_data)}")

        # Verify output data structure matches reference format
        expected_columns = 78
        actual_columns = len(scenario_data.columns)
        dual_print(f"Column count verification: {actual_columns} (expected: {expected_columns})")
        if actual_columns != expected_columns:
            dual_print(f"WARNING: Column count mismatch! Expected {expected_columns}, got {actual_columns}")
            dual_print(f"Columns: {list(scenario_data.columns)}")

    dual_print(f"\nAll {num_simulations} scenarios completed!")
    dual_print("Each scenario saved to a separate CSV file with 78 columns matching the reference structure")


if __name__ == "__main__":
    """
    Entry point for script execution

    When run directly, this script:
    - Creates a timestamped console output log file
    - Executes the main analysis function with dual output (console + file)
    - Captures all print statements for later review and debugging
    """
    timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    save_console_output(f'console_output_sobol_switching_{timestamp}.txt', main)
