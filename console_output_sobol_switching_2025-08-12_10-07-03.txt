=== Enhanced Modal Analysis with Sobol Sampling and Dynamic Load Switching ===
Found 3 generators, 9 buses, 6 lines
Using existing data folder: data
Found 0 load switches:

Generating Sobol samples for 2 simulations...

=== Running Simulation 1/2 ===
Scaling factors - a: 1.007, b: 1.174, c: 0.650
Collected 100 samples for scenario 1...
Switching at 2.00s: A=True, B=False, C=True
Collected 200 samples for scenario 1...
Switching at 4.00s: A=True, B=False, C=False
Collected 300 samples for scenario 1...
Switching at 6.02s: A=True, B=False, C=False
Collected 400 samples for scenario 1...
Switching at 8.02s: A=True, B=True, C=False
Collected 500 samples for scenario 1...
Switching at 10.02s: A=False, B=True, C=False
Collected 600 samples for scenario 1...
Switching at 12.02s: A=True, B=False, C=False
Collected 700 samples for scenario 1...
Switching at 14.02s: A=True, B=True, C=True
Collected 800 samples for scenario 1...
Switching at 16.02s: A=True, B=True, C=False
Collected 900 samples for scenario 1...
Switching at 18.02s: A=True, B=False, C=True
Collected 1000 samples for scenario 1...
Switching at 20.02s: A=True, B=False, C=True
Collected 1100 samples for scenario 1...
Switching at 22.02s: A=True, B=True, C=False
Collected 1200 samples for scenario 1...
Switching at 24.02s: A=True, B=False, C=False
Collected 1300 samples for scenario 1...
Switching at 26.02s: A=True, B=True, C=False
Collected 1400 samples for scenario 1...
Switching at 28.02s: A=True, B=False, C=True
Collected 1500 samples for scenario 1...
Switching at 30.02s: A=False, B=True, C=True
Collected 1600 samples for scenario 1...
Switching at 32.02s: A=True, B=False, C=False
Collected 1700 samples for scenario 1...
Switching at 34.02s: A=False, B=False, C=False
Collected 1800 samples for scenario 1...
Switching at 36.00s: A=True, B=True, C=True
Collected 1900 samples for scenario 1...
Switching at 38.00s: A=False, B=True, C=False
Collected 2000 samples for scenario 1...
Scenario 1 saved to: data\modal_analysis_scenario_1_2025-08-12_10-08-00.csv
Scenario 1 samples: 2000
Column count verification: 78 (expected: 78)

=== Running Simulation 2/2 ===
Scaling factors - a: 1.007, b: 1.174, c: 0.650
Collected 100 samples for scenario 2...
Switching at 2.00s: A=False, B=False, C=True
Collected 200 samples for scenario 2...
Switching at 4.00s: A=False, B=False, C=True
Collected 300 samples for scenario 2...
Switching at 6.02s: A=False, B=False, C=True
Collected 400 samples for scenario 2...
Switching at 8.02s: A=True, B=True, C=False
Collected 500 samples for scenario 2...
Switching at 10.02s: A=True, B=True, C=True
Collected 600 samples for scenario 2...
Switching at 12.02s: A=False, B=True, C=False
Collected 700 samples for scenario 2...
Switching at 14.02s: A=False, B=False, C=False
Collected 800 samples for scenario 2...
Switching at 16.02s: A=False, B=False, C=True
Collected 900 samples for scenario 2...
Switching at 18.02s: A=True, B=False, C=False
Collected 1000 samples for scenario 2...
Switching at 20.02s: A=False, B=True, C=True
Collected 1100 samples for scenario 2...
Switching at 22.02s: A=True, B=False, C=True
Collected 1200 samples for scenario 2...
Switching at 24.02s: A=True, B=False, C=False
Collected 1300 samples for scenario 2...
Switching at 26.02s: A=True, B=True, C=False
Collected 1400 samples for scenario 2...
Switching at 28.02s: A=True, B=False, C=True
Collected 1500 samples for scenario 2...
Switching at 30.02s: A=False, B=True, C=False
Collected 1600 samples for scenario 2...
Switching at 32.02s: A=True, B=True, C=False
Collected 1700 samples for scenario 2...
Switching at 34.02s: A=True, B=True, C=True
Collected 1800 samples for scenario 2...
Switching at 36.00s: A=True, B=False, C=True
Collected 1900 samples for scenario 2...
Switching at 38.00s: A=False, B=True, C=True
Collected 2000 samples for scenario 2...
Scenario 2 saved to: data\modal_analysis_scenario_2_2025-08-12_10-08-51.csv
Scenario 2 samples: 2000
Column count verification: 78 (expected: 78)

All 2 scenarios completed!
Each scenario saved to a separate CSV file with 78 columns matching the reference structure
