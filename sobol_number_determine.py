"""
Sobol Convergence Validation

This script determines the minimum suitable value for `num_simulations` by:
1. Incrementally increasing the sample size and observing the statistical distribution of key metrics (e.g., node voltage, line power).
2. Performing sensitivity analysis using Sobol sensitivity indices to check the stability of load impacts on outputs.

"""

from SALib.analyze import sobol
from SALib.sample import saltelli
import numpy as np
import pandas as pd

# Define the problem with 3 load parameters and their bounds
problem = {
    'num_vars': 3,
    'names': ['Load1', 'Load2', 'Load3'],
    'bounds': [[0.5, 1.2]] * 3
}

def power_flow_model(loads):
    """
    Placeholder for the actual power flow model.
    Replace this with the actual implementation to compute outputs based on load inputs.

    Args:
        loads: List of load scaling factors [Load1, Load2, Load3].

    Returns:
        Simulated output (e.g., node voltage or line power).
    """
    return np.random.rand()  # Replace with actual model computation

def run_sobol_analysis(initial_samples=128, max_samples=1024, tolerance=0.05):
    """
    Perform Sobol convergence validation by incrementally increasing sample size.

    Args:
        initial_samples: Initial number of samples to start with.
        max_samples: Maximum number of samples to test.
        tolerance: Convergence tolerance for mean/variance changes (<5%).

    Returns:
        DataFrame summarizing the results for each sample size.
    """
    results = []

    current_samples = initial_samples
    previous_mean = None
    previous_variance = None

    while current_samples <= max_samples:
        print(f"Running Sobol analysis with {current_samples} samples...")

        # Generate Sobol samples using Saltelli's scheme
        samples = saltelli.sample(problem, current_samples, calc_second_order=True)

        # Run the power flow model for each sample
        outputs = np.array([power_flow_model(sample) for sample in samples])

        # Compute mean and variance of the outputs
        mean_output = np.mean(outputs)
        variance_output = np.var(outputs)

        # Perform sensitivity analysis
        Si = sobol.analyze(problem, outputs)

        # Store results
        results.append({
            'Samples': current_samples,
            'Mean': mean_output,
            'Variance': variance_output,
            'S1': Si['S1'],
            'S1_conf': Si['S1_conf']
        })

        # Check for convergence
        if previous_mean is not None and previous_variance is not None:
            mean_change = abs(mean_output - previous_mean) / previous_mean
            variance_change = abs(variance_output - previous_variance) / previous_variance

            if mean_change < tolerance and variance_change < tolerance:
                print(f"Convergence achieved with {current_samples} samples.")
                break

        # Update for next iteration
        previous_mean = mean_output
        previous_variance = variance_output
        current_samples *= 2  # Double the sample size for the next iteration

    # Convert results to DataFrame for analysis
    return pd.DataFrame(results)

def test_specific_sample_size(sample_size=128):
    """
    Test a specific sample size to see if it provides reliable results.

    Args:
        sample_size: Number of samples to test.

    Returns:
        Dictionary with statistics and sensitivity analysis results.
    """
    print(f"Testing specific sample size: {sample_size}")

    # Generate samples
    samples = saltelli.sample(problem, sample_size, calc_second_order=True)

    # Run multiple trials to check stability
    num_trials = 5
    trial_results = []

    for trial in range(num_trials):
        print(f"Trial {trial+1}/{num_trials}")
        outputs = np.array([power_flow_model(sample) for sample in samples])
        Si = sobol.analyze(problem, outputs)

        trial_results.append({
            'Mean': np.mean(outputs),
            'Variance': np.var(outputs),
            'S1': Si['S1'],
            'S1_conf': Si['S1_conf']
        })

    # Analyze stability across trials
    means = np.array([result['Mean'] for result in trial_results])
    variances = np.array([result['Variance'] for result in trial_results])

    # Calculate coefficient of variation (CV) as a stability metric
    mean_cv = np.std(means) / np.mean(means) if np.mean(means) != 0 else float('inf')
    variance_cv = np.std(variances) / np.mean(variances) if np.mean(variances) != 0 else float('inf')

    result = {
        'Sample_Size': sample_size,
        'Mean_CV': mean_cv,
        'Variance_CV': variance_cv,
        'Trial_Results': trial_results
    }

    print(f"Results for {sample_size} samples:")
    print(f"  Mean CV: {mean_cv:.4f} (lower is more stable)")
    print(f"  Variance CV: {variance_cv:.4f} (lower is more stable)")
    print(f"  Stability assessment: {'Good' if mean_cv < 0.05 and variance_cv < 0.05 else 'Poor'}")

    return result

if __name__ == "__main__":
    # Test if 128 samples is sufficient
    test_result = test_specific_sample_size(128)

    # Save test result
    with open("sobol_128_sample_test_results.txt", "w") as f:
        f.write(f"Sample Size: {test_result['Sample_Size']}\n")
        f.write(f"Mean CV: {test_result['Mean_CV']:.6f}\n")
        f.write(f"Variance CV: {test_result['Variance_CV']:.6f}\n")
        f.write(f"Stability Assessment: {'Good' if test_result['Mean_CV'] < 0.05 and test_result['Variance_CV'] < 0.05 else 'Poor'}\n\n")

        for i, trial in enumerate(test_result['Trial_Results']):
            f.write(f"Trial {i+1}:\n")
            f.write(f"  Mean: {trial['Mean']:.6f}\n")
            f.write(f"  Variance: {trial['Variance']:.6f}\n")
            f.write(f"  S1: {trial['S1']}\n")
            f.write(f"  S1_conf: {trial['S1_conf']}\n\n")

    print("Test results saved to sobol_128_sample_test_results.txt")

    # Run the full convergence analysis starting from 128 samples
    results_df = run_sobol_analysis()

    # Save results to a CSV file
    results_df.to_csv("sobol_convergence_results.csv", index=False)
    print("Convergence results saved to sobol_convergence_results.csv")
