"""
Eigenvalue Prediction Inference Script

This script demonstrates how to use the trained eigenvalue prediction model
for real-time inference on new power system data.

Usage:
    python eigenvalue_inference.py --input_file path/to/new_data.csv
    python eigenvalue_inference.py --input_file data/modal_analysis_scenario_1_2025-08-12_13-55-26.csv
"""

import os
import numpy as np
import torch
import torch.nn as nn
import pickle
import argparse
import pandas as pd
from datetime import datetime

# === Configuration ===
MODEL_PATH = "FNN_NARX_eigenvalue_model.pth"
X_SCALER_PATH = "X_scaler.pkl"
Y_SCALER_PATH = "Y_scaler.pkl"

INPUT_LAG = 3
OUTPUT_LAG = 2
HIDDEN_SIZE = 128
OUTPUT_CHANNELS = 22

# Input and output columns (same as training)
INPUT_COLUMNS = list(range(2, 29))  # Columns 2-28 (27 features)
OUTPUT_COLUMNS = list(range(41, 63))  # Columns 41-62 (22 eigenvalue features)

# Eigenvalue names for output
EIGENVALUE_NAMES = []
for i in range(11):  # EV_0 to EV_10
    EIGENVALUE_NAMES.extend([f'EV_{i}_real', f'EV_{i}_imag'])

# === Model Definition ===
class FNN_NARX_Eigenvalue(nn.Module):
    """Feedforward Neural Network for NARX eigenvalue modeling."""
    def __init__(self, input_size, hidden_size, output_size):
        super(FNN_NARX_Eigenvalue, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, output_size)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.1)

    def forward(self, x, y):
        combined_input = torch.cat((x, y), dim=1)
        out = self.relu(self.fc1(combined_input))
        out = self.dropout(out)
        out = self.relu(self.fc2(out))
        out = self.dropout(out)
        out = self.fc3(out)
        return out

class EigenvaluePredictor:
    """Class for eigenvalue prediction inference."""
    
    def __init__(self, model_path=MODEL_PATH, x_scaler_path=X_SCALER_PATH, y_scaler_path=Y_SCALER_PATH):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = None
        self.X_scaler = None
        self.Y_scaler = None
        
        self.load_model_and_scalers(model_path, x_scaler_path, y_scaler_path)
    
    def load_model_and_scalers(self, model_path, x_scaler_path, y_scaler_path):
        """Load the trained model and scalers."""
        print("Loading model and scalers...")
        
        # Load scalers
        with open(x_scaler_path, 'rb') as f:
            self.X_scaler = pickle.load(f)
        with open(y_scaler_path, 'rb') as f:
            self.Y_scaler = pickle.load(f)
        
        # Calculate input size
        input_size = INPUT_LAG * len(INPUT_COLUMNS) + OUTPUT_LAG * len(OUTPUT_COLUMNS)
        
        # Load model
        self.model = FNN_NARX_Eigenvalue(input_size, HIDDEN_SIZE, OUTPUT_CHANNELS).to(self.device)
        self.model.load_state_dict(torch.load(model_path, map_location=self.device))
        self.model.eval()
        
        print(f"Model loaded successfully on {self.device}")
    
    def preprocess_data(self, data):
        """Preprocess input data for prediction."""
        # Extract input and output features
        X = data[:, INPUT_COLUMNS].astype(np.float32)
        Y = data[:, OUTPUT_COLUMNS].astype(np.float32)
        
        # Normalize data
        X_flat = X.reshape(-1, X.shape[-1])
        Y_flat = Y.reshape(-1, Y.shape[-1])
        
        X_scaled_flat = self.X_scaler.transform(X_flat)
        Y_scaled_flat = self.Y_scaler.transform(Y_flat)
        
        X_scaled = X_scaled_flat.reshape(X.shape)
        Y_scaled = Y_scaled_flat.reshape(Y.shape)
        
        return X_scaled, Y_scaled
    
    def predict_single_timestep(self, X_history, Y_history):
        """Predict eigenvalues for a single timestep given history."""
        # Ensure we have enough history
        if X_history.shape[0] < INPUT_LAG or Y_history.shape[0] < OUTPUT_LAG:
            raise ValueError(f"Insufficient history. Need at least {INPUT_LAG} X samples and {OUTPUT_LAG} Y samples")
        
        # Extract sequences
        x_seq = X_history[-INPUT_LAG:, :].reshape(-1)
        y_seq = Y_history[-OUTPUT_LAG:, :].reshape(-1)
        
        # Convert to tensors
        x_seq = torch.tensor(x_seq, dtype=torch.float32).unsqueeze(0).to(self.device)
        y_seq = torch.tensor(y_seq, dtype=torch.float32).unsqueeze(0).to(self.device)
        
        # Predict
        with torch.no_grad():
            pred_scaled = self.model(x_seq, y_seq)
            pred_scaled = pred_scaled.cpu().numpy().flatten()
        
        # Denormalize prediction
        pred_denorm = self.Y_scaler.inverse_transform(pred_scaled.reshape(1, -1)).flatten()
        
        return pred_denorm
    
    def predict_sequence(self, data):
        """Predict eigenvalues for an entire sequence."""
        X_scaled, Y_scaled = self.preprocess_data(data)
        
        predictions = []
        timestamps = []
        
        num_timesteps = X_scaled.shape[0]
        
        for t in range(max(INPUT_LAG, OUTPUT_LAG), num_timesteps):
            # Get history
            X_history = X_scaled[:t, :]
            Y_history = Y_scaled[:t, :]
            
            # Predict
            pred = self.predict_single_timestep(X_history, Y_history)
            predictions.append(pred)
            timestamps.append(t)
        
        return np.array(predictions), timestamps
    
    def predict_from_file(self, file_path, output_file=None):
        """Predict eigenvalues from a CSV file."""
        print(f"Loading data from {file_path}...")
        
        # Load data
        data = np.loadtxt(file_path, delimiter=',', skiprows=1)
        
        # Make predictions
        predictions, timestamps = self.predict_sequence(data)
        
        print(f"Generated {len(predictions)} predictions")
        
        # Create results DataFrame
        results_df = pd.DataFrame(predictions, columns=EIGENVALUE_NAMES)
        results_df.insert(0, 'Timestep', timestamps)
        
        # Add timestamp if available in original data
        if data.shape[1] > 0:  # Assuming first column is time
            time_values = data[timestamps, 0] if data.shape[1] > 0 else timestamps
            results_df.insert(1, 'Time', time_values)
        
        # Save results if output file specified
        if output_file:
            results_df.to_csv(output_file, index=False)
            print(f"Results saved to {output_file}")
        
        return results_df
    
    def get_stability_assessment(self, eigenvalues):
        """Assess power system stability based on eigenvalues."""
        # Simple stability assessment based on real parts
        real_parts = eigenvalues[::2]  # Every even index is real part
        
        # Count unstable eigenvalues (positive real parts)
        unstable_count = np.sum(real_parts > 0.01)  # Small threshold for numerical stability
        
        # Calculate stability margin (most positive real part)
        stability_margin = np.max(real_parts)
        
        # Determine stability status
        if unstable_count == 0:
            status = "STABLE"
        elif unstable_count <= 2:
            status = "MARGINALLY_STABLE"
        else:
            status = "UNSTABLE"
        
        return {
            'status': status,
            'unstable_count': unstable_count,
            'stability_margin': stability_margin,
            'total_eigenvalues': len(real_parts)
        }

def main():
    parser = argparse.ArgumentParser(description='Eigenvalue Prediction Inference')
    parser.add_argument('--input_file', type=str, required=True,
                       help='Path to input CSV file')
    parser.add_argument('--output_file', type=str, default=None,
                       help='Path to save prediction results (optional)')
    parser.add_argument('--show_stability', action='store_true',
                       help='Show stability assessment for each prediction')
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input_file):
        print(f"Error: Input file {args.input_file} not found!")
        return
    
    # Check if model files exist
    if not all(os.path.exists(f) for f in [MODEL_PATH, X_SCALER_PATH, Y_SCALER_PATH]):
        print("Error: Model or scaler files not found!")
        print("Please ensure the following files exist:")
        print(f"  - {MODEL_PATH}")
        print(f"  - {X_SCALER_PATH}")
        print(f"  - {Y_SCALER_PATH}")
        return
    
    # Create predictor
    predictor = EigenvaluePredictor()
    
    # Generate output filename if not provided
    if args.output_file is None:
        base_name = os.path.splitext(os.path.basename(args.input_file))[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output_file = f"eigenvalue_predictions_{base_name}_{timestamp}.csv"
    
    # Make predictions
    try:
        results_df = predictor.predict_from_file(args.input_file, args.output_file)
        
        print("\n=== Prediction Summary ===")
        print(f"Input file: {args.input_file}")
        print(f"Output file: {args.output_file}")
        print(f"Number of predictions: {len(results_df)}")
        
        # Show sample predictions
        print("\n=== Sample Predictions (first 5 rows) ===")
        print(results_df.head().to_string(index=False))
        
        # Show stability assessment if requested
        if args.show_stability:
            print("\n=== Stability Assessment (last prediction) ===")
            last_prediction = results_df.iloc[-1][EIGENVALUE_NAMES].values
            stability = predictor.get_stability_assessment(last_prediction)
            
            print(f"Status: {stability['status']}")
            print(f"Unstable eigenvalues: {stability['unstable_count']}/{stability['total_eigenvalues']}")
            print(f"Stability margin: {stability['stability_margin']:.6f}")
        
        print("\nInference completed successfully!")
        
    except Exception as e:
        print(f"Error during inference: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
