import os
import platform
import random
import time
import winsound
import sys
import numpy as np
# import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import train_test_split
from torch.utils.data import Dataset, DataLoader
# import onnx
# import onnxruntime as ort
# from torch.cuda.amp import autocast, GradScaler
import traceback
from sklearn.preprocessing import MinMaxScaler
from torch.amp import autocast, GradScaler
# === Global Config ===
SEED = 42
DATA_DIR = 'D:/data/time_series/simulation_gfm/gfm_sim_disturbance/'
MODEL_SAVE_PATH = "FNN_NARX_model_test_normal.pth"
# DATA_DIR = 'D:/data/gfm_no_noice_20g/gfm_sim_abc/'
# MODEL_SAVE_PATH = "FNN_NARX_model_20g_sigmoid.pth"
# MODEL_SAVE_PATH = "FNN_NARX_model_20g_reduce_normalize.pth"

INPUT_LAG = 5
OUTPUT_LAG = 3
HIDDEN_SIZE = 32
# BATCH_SIZE = 1024 # originally 128
BATCH_SIZE = 1024   # originally 128, 2048, 512

EPOCHS = 8
LEARNING_RATE = 0.001
# NOISE_RATIO = 0.0316
NOISE_RATIO = 0
OUTPUT_CHANNELS=5

# === save the console output to a file ===







import sys
import traceback
#
# def save_console_output(file_path, func, *args, **kwargs):
#     # Save the original standard output
#     original_stdout = sys.stdout
#     current_file = os.path.basename(__file__)
#
#     print(f"Executing file: {os.path.abspath(__file__)}")
#
#     # Open a file to write the console output with utf-8 encoding
#     with open(file_path, 'w', encoding='utf-8') as f:
#         # Redirect standard output to the file
#         sys.stdout = f
#         print(f"Executing file: {os.path.abspath(__file__)}")
#
#         try:
#             # Execute the function and capture its output
#             func(*args, **kwargs)
#         except Exception as e:
#             # Print the exception traceback to the file
#             traceback.print_exc(file=f)
#
#     # Restore the original standard output
#     sys.stdout = original_stdout
#
# # def main():
# #     print("Using device: cuda")
# #     # Your existing code...
# #
# # if __name__ == '__main__':
# #     save_console_output('console_output.txt', main)
# # === Utility Functions ===

def save_console_output(file_path, func, *args, **kwargs):
    # Save the original standard output
    original_stdout = sys.stdout

    print(f"Executing file: {os.path.abspath(__file__)}")

    # Open a file to write the console output with utf-8 encoding
    with open(file_path, 'w', encoding='utf-8') as f:
        # Create dual output that writes to both console and file
        dual_output = DualOutput(f)
        sys.stdout = dual_output

        # This will now appear in both console and file
        print(f"Executing file: {os.path.abspath(__file__)}")

        try:
            # Execute the function and capture its output
            func(*args, **kwargs)
        except Exception as e:
            # Print the exception traceback to both console and file
            traceback.print_exc()

    # Restore the original standard output
    sys.stdout = original_stdout


def set_seed(seed=SEED):
    """Sets random seeds for reproducibility."""
    torch.manual_seed(seed)
    np.random.seed(seed)
    random.seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)


def get_device():
    """Detects the device and sets CPU/GPU configurations."""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    if device.type == 'cpu' and platform.machine() in ['x86_64', 'arm64']:
        torch.set_num_threads(os.cpu_count() - (2 if platform.machine() == 'x86_64' else 0))
    return device


# === Data Loading & Processing ===
def process_boolean_output(output):
    """Processes boolean output columns to float type."""
    return output.astype(float)


# def load_data(data_dir=DATA_DIR):
#     """Loads and preprocesses the dataset."""
#     X_data, Y_data = [], []
#     for i in range(2107):   # previous 108
#         # data = np.loadtxt(f'{data_dir}output_{i * 5 + 1}.csv', delimiter=',', skiprows=1)
#         data = np.loadtxt(f'{data_dir}output_{i * 5 + 1}.csv', delimiter=',', skiprows=1)
#
#         X = data[:, [5, 6, 8, 9, 10, 11, 12, 13, 14]]
#         Y = data[:, [1, 2, 3, 4, 15, 16, 7]]
#         Y[:, 6] = process_boolean_output(Y[:, 6])  # Column index for boolean processing
#         X_data.append(X)
#         Y_data.append(Y)
#     X_data = np.array(X_data, dtype=np.float32)
#     Y_data = np.array(Y_data, dtype=np.float32)
#
#     return train_test_split(X_data, Y_data, test_size=0.2, random_state=SEED)
#
#
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler


def load_data(data_dir=DATA_DIR):
    """Loads and preprocesses the dataset."""
    X_data, Y_data = [], []
    scaler = MinMaxScaler()  # Initialize the MinMaxScaler

    for i in range(108):  # previous 108  / 2107
        data = np.loadtxt(f'{data_dir}output_{i * 9 + 1}.csv', delimiter=',', skiprows=1)
        X = data[:, [5, 6, 8, 9, 10, 11, 12, 13, 14]]
        Y = data[:, [1, 2, 15, 16, 7]]
        X_data.append(X)
        Y_data.append(Y)

    X_data = np.array(X_data, dtype=np.float32)
    Y_data = np.array(Y_data, dtype=np.float32)

    return train_test_split(X_data, Y_data, test_size=0.2, random_state=SEED)


class TimeSeriesDataset(Dataset):
    """Custom Dataset for Time-Series Data."""
    def __init__(self, X_data, Y_data, input_lag, output_lag, noise_ratio=NOISE_RATIO):
        self.X_data = X_data
        self.Y_data = Y_data
        self.input_lag = input_lag
        self.output_lag = output_lag
        self.noise_ratio = noise_ratio
        self.num_timesteps = X_data.shape[1]
        self.num_samples = X_data.shape[0] * (self.num_timesteps - max(input_lag, output_lag))

    def __len__(self):
        return self.num_samples

    def __getitem__(self, idx):
        experiment_idx = idx // (self.num_timesteps - max(self.input_lag, self.output_lag))
        time_idx = idx % (self.num_timesteps - max(self.input_lag, self.output_lag)) + max(self.input_lag,
                                                                                           self.output_lag)

        # Extract sequences
        x_seq = self.X_data[experiment_idx, time_idx - self.input_lag:time_idx, :]
        y_seq = self.Y_data[experiment_idx, time_idx - self.output_lag:time_idx, :]
        target_y = self.Y_data[experiment_idx, time_idx, :]

        # Convert NumPy arrays to PyTorch tensors
        x_seq = torch.tensor(x_seq, dtype=torch.float32)
        y_seq = torch.tensor(y_seq, dtype=torch.float32)
        target_y = torch.tensor(target_y, dtype=torch.float32)

        # Add white noise to sequences
        # noise_x_seq = torch.randn_like(x_seq) * self.noise_ratio * torch.abs(x_seq)
        # noise_y_seq = torch.randn_like(y_seq) * self.noise_ratio * torch.abs(y_seq)
        # noise_target_y = torch.randn_like(target_y) * self.noise_ratio * torch.abs(target_y)
        #
        # noisy_x_seq = x_seq + noise_x_seq
        # noisy_y_seq = y_seq + noise_y_seq
        # noisy_target_y = target_y + noise_target_y
        noisy_x_seq = x_seq
        noisy_y_seq = y_seq
        noisy_target_y = target_y
        return noisy_x_seq.reshape(-1), noisy_y_seq.reshape(-1), noisy_target_y


# === Model Definition ===
class FNN_NARX(nn.Module):
    """Feedforward Neural Network for NARX modeling."""
    def __init__(self, input_size, hidden_size, output_size):
        super(FNN_NARX, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.relu = nn.ReLU()
        self.fc2 = nn.Linear(hidden_size, output_size)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x, y):
        combined_input = torch.cat((x, y), dim=1)
        out = self.relu(self.fc1(combined_input))
        out = self.fc2(out)
        out[:, -1] = self.sigmoid(out[:, -1])  # Apply sigmoid to the last output channel
        return out


# # === Training and Evaluation ===
# def train_model(model, train_loader, criterion, optimizer, device, epochs=EPOCHS):
#     """Trains the model with the given DataLoader."""
#     model.train()
#     for epoch in range(epochs):
#         epoch_loss = 0
#         all_predictions = []
#         all_targets = []
#         start_time = time.time()  # Start tracking time for the training epoch
#         for x_seq, y_seq, target_y in train_loader:
#             x_seq, y_seq, target_y = x_seq.to(device), y_seq.to(device), target_y.to(device)
#             outputs = model(x_seq, y_seq)
#             loss = criterion(outputs, target_y)
#
#             optimizer.zero_grad()
#             loss.backward()
#             optimizer.step()
#             epoch_loss += loss.item()
#             # Collect predictions and targets for metrics computation
#             all_predictions.append(outputs.detach().cpu().numpy())
#             all_targets.append(target_y.detach().cpu().numpy())
#
#         # End time for the epoch
#         end_time = time.time()
#
#         # Concatenate all predictions and targets
#         all_predictions = np.concatenate(all_predictions, axis=0)
#         all_targets = np.concatenate(all_targets, axis=0)
#         # Compute additional metrics
#         mse = mean_squared_error(all_targets, all_predictions)
#         rmse = np.sqrt(mse)
#         r2 = r2_score(all_targets, all_predictions)
#         fit_loss = np.mean(np.abs(all_targets - all_predictions) / (np.abs(all_targets) + 1e-8))
#
#         # Print metrics
#         print(f"Epoch completed in {(end_time - start_time):.2f} seconds")
#         print(f"Loss (MSE): {epoch_loss / len(train_loader):.7f}")
#         print(f"RMSE: {rmse:.7f}")
#         print(f"R²: {r2:.7f}")
#         print(f"Fit Loss: {fit_loss:.7f}")
#         print(f"Epoch [{epoch + 1}/{epochs}] Loss: {epoch_loss / len(train_loader):.7f}")
class DualOutput:
    def __init__(self, file_handle):
        self.file = file_handle
        self.console = sys.__stdout__  # Original stdout

    def write(self, text):
        self.console.write(text)  # Write to console
        self.file.write(text)     # Write to file
        self.console.flush()      # Ensure immediate console output
        self.file.flush()         # Ensure immediate file output

    def flush(self):
        self.console.flush()
        self.file.flush()

import torch
import numpy as np
from sklearn.metrics import mean_squared_error, r2_score
import time


def train_model(model, train_loader, criterion, optimizer, device, epochs=EPOCHS):
    """Trains the model with mixed precision using the given DataLoader."""
    model.train()
    # Fix GradScaler initialization - use the new API
    scaler = GradScaler() if device.type == 'cuda' else None

    for epoch in range(epochs):
        check_gpu_status()

        epoch_loss = 0
        all_predictions = []
        all_targets = []
        start_time = time.time()

        for x_seq, y_seq, target_y in train_loader:
            x_seq, y_seq, target_y = x_seq.to(device), y_seq.to(device), target_y.to(device)

            # Forward pass under mixed precision
            if device.type == 'cuda':
                with autocast('cuda'):
                    outputs = model(x_seq, y_seq)
                    loss = criterion(outputs, target_y)
            else:
                outputs = model(x_seq, y_seq)
                loss = criterion(outputs, target_y)

            optimizer.zero_grad()

            # Backward pass and gradient scaling
            if device.type == 'cuda' and scaler is not None:
                scaler.scale(loss).backward()
                scaler.step(optimizer)
                scaler.update()
            else:
                loss.backward()
                optimizer.step()

            epoch_loss += loss.item()
            all_predictions.append(outputs.detach().cpu().numpy())
            all_targets.append(target_y.detach().cpu().numpy())

        # End time for the epoch
        end_time = time.time()

        # Concatenate all predictions and targets
        all_predictions = np.concatenate(all_predictions, axis=0)
        all_targets = np.concatenate(all_targets, axis=0)

        # Compute additional metrics
        mse = mean_squared_error(all_targets, all_predictions)
        rmse = np.sqrt(mse)
        r2 = r2_score(all_targets, all_predictions)
        fit_loss = np.mean(np.abs(all_targets - all_predictions) / (np.abs(all_targets) + 1e-8))

        # Print metrics
        print(f"Epoch completed in {(end_time - start_time):.2f} seconds")
        print(f"Loss (MSE): {epoch_loss / len(train_loader):.7f}")
        print(f"RMSE: {rmse:.7f}")
        print(f"R²: {r2:.7f}")
        print(f"Fit Loss: {fit_loss:.7f}")
        print(f"Epoch [{epoch + 1}/{epochs}] Loss: {epoch_loss / len(train_loader):.7f}")


# bar plot for the loss per output
# def evaluate_model_per_output(model, val_loader, device):
#     """
#     Evaluate model on validation data and calculate loss for each individual output.
#
#     Args:
#         model: The trained model.
#         val_loader: DataLoader containing validation data.
#         device: The device (CPU or GPU).
#
#     Returns:
#         List of average losses for each output.
#     """
#     model.eval()
#     output_dim = 7  # Number of outputs
#     output_losses = np.zeros(output_dim)
#     counts = np.zeros(output_dim)  # To track number of samples per output
#
#     with torch.no_grad():
#         for x_seq, y_seq, target_y in val_loader:
#             x_seq, y_seq, target_y = x_seq.to(device), y_seq.to(device), target_y.to(device)
#             x_seq = x_seq.view(x_seq.size(0), -1)
#             y_seq = y_seq.view(y_seq.size(0), -1)
#             outputs = model(x_seq, y_seq)
#
#             # Calculate loss for each output
#             for i in range(output_dim):
#                 criterion = nn.MSELoss()  # Define per-output criterion
#                 output_losses[i] += criterion(outputs[:, i], target_y[:, i]).item()
#                 counts[i] += 1
#
#     # Average the loss per output
#     average_losses = output_losses / counts
#     return average_losses, output_losses
#
#
# def plot_losses(losses):
#     """
#     Plot the losses for each output.
#
#     Args:
#         losses: List of average losses for each output.
#     """
#     output_indices = range(1, len(losses) + 1)
#     plt.figure(figsize=(8, 5))
#     plt.bar(output_indices, losses, color='skyblue')
#     plt.xlabel("Output Index")
#     plt.ylabel("Average Loss")
#     plt.title("Loss per Output")
#     plt.xticks(output_indices)
#     plt.show()

## residuals
def evaluate_model_per_output(model, val_loader, criterion, device, num_outputs=OUTPUT_CHANNELS):
    model.eval()
    val_loss = 0.0
    all_residuals = [[] for _ in range(num_outputs)]  # To store residuals for each output

    with torch.no_grad():
        for x_seq, y_seq, target_y in val_loader:
            x_seq, y_seq, target_y = x_seq.to(device), y_seq.to(device), target_y.to(device)

            # Reshape inputs as required
            x_seq = x_seq.view(x_seq.size(0), -1)
            y_seq = y_seq.view(y_seq.size(0), -1)

            # Generate predictions
            outputs = model(x_seq, y_seq)

            # Compute loss
            loss = criterion(outputs, target_y)
            val_loss += loss.item()

            # Calculate residuals (difference between targets and predictions)
            residuals = (target_y - outputs).cpu().numpy()

            # Collect residuals for each output
            for i in range(num_outputs):
                all_residuals[i].extend(residuals[:, i])

    return val_loss / len(val_loader), all_residuals


# import matplotlib.pyplot as plt


# def plot_residuals(all_residuals):
#     """
#     Plot histograms of residuals for each output.
#
#     Parameters:
#         all_residuals (list of lists): Residuals for each output.
#     """
#     num_outputs = len(all_residuals)
#     fig, axes = plt.subplots(1, num_outputs, figsize=(15, 5), sharey=True)
#
#     for i in range(num_outputs):
#         axes[i].hist(all_residuals[i], bins=20, alpha=0.7, color='blue', edgecolor='black')
#         axes[i].set_title(f'Output {i + 1}')
#         axes[i].set_xlabel('Residual')
#         axes[i].set_ylabel('Frequency')
#     #
#     # plt.tight_layout()
#     # plt.show()

def export_model_to_onnx(model, x_example, y_example, onnx_file_path, opset_version=11):
    """
    Exports a PyTorch model to ONNX format.

    Args:
        model (torch.nn.Module): The PyTorch model to export.
        x_example (torch.Tensor): Example input tensor x.
        y_example (torch.Tensor): Example input tensor y.
        onnx_file_path (str): Path to save the ONNX file.
        opset_version (int): ONNX opset version. Default is 11.
    """
    model.eval()  # Ensure the model is in evaluation mode
    torch.onnx.export(
        model,  # The model to export
        (x_example, y_example),  # Example inputs as a tuple
        onnx_file_path,  # Path to save the ONNX file
        export_params=True,  # Store the trained parameters
        opset_version=opset_version,  # ONNX opset version
        do_constant_folding=True,  # Perform constant folding optimization
        input_names=['x', 'y'],  # Names for the inputs
        output_names=['output'],  # Name for the output
        dynamic_axes={  # Dynamic axes for variable input sizes
            'x': {0: 'batch_size'},
            'y': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        }
    )
    print(f"Model exported to ONNX format at {onnx_file_path}")





# import onnxruntime as ort
import numpy as np

def evaluate_onnx_model(session, val_loader, criterion, device, num_outputs=OUTPUT_CHANNELS):
    # Get input and output names from the ONNX model
    input_x_name = session.get_inputs()[0].name
    input_y_name = session.get_inputs()[1].name
    output_name = session.get_outputs()[0].name

    val_loss = 0.0
    all_residuals = [[] for _ in range(num_outputs)]  # To store residuals for each output

    with torch.no_grad():
        for x_seq, y_seq, target_y in val_loader:
            # Move data to the specified device
            x_seq, y_seq, target_y = x_seq.to(device), y_seq.to(device), target_y.to(device)

            # Reshape inputs as required and convert to NumPy arrays
            x_seq_np = x_seq.view(x_seq.size(0), -1).cpu().numpy().astype(np.float32)
            y_seq_np = y_seq.view(y_seq.size(0), -1).cpu().numpy().astype(np.float32)

            # Perform ONNX inference
            outputs = session.run([output_name], {input_x_name: x_seq_np, input_y_name: y_seq_np})
            outputs = outputs[0]  # Extract output array

            # Compute loss using the criterion
            loss = criterion(torch.tensor(outputs, device=device), target_y)
            val_loss += loss.item()

            # Calculate residuals (difference between targets and predictions)
            residuals = (target_y.cpu().numpy() - outputs)

            # Collect residuals for each output
            for i in range(num_outputs):
                all_residuals[i].extend(residuals[:, i])

    return val_loss / len(val_loader), all_residuals
# import onnxruntime as ort
import numpy as np
import torch

def evaluate_onnx_model_per_channel(session, val_loader, criterion, device, num_outputs=OUTPUT_CHANNELS):
    '''

    :param session:
    :param val_loader:
    :param criterion:
    :param device:
    :param num_outputs:
    :return:
    '''
    # Get input and output names from the ONNX model
    input_x_name = session.get_inputs()[0].name
    input_y_name = session.get_inputs()[1].name
    output_name = session.get_outputs()[0].name

    val_loss = 0.0
    all_residuals = [[] for _ in range(num_outputs)]  # To store residuals for each output

    with torch.no_grad():
        for x_seq, y_seq, target_y in val_loader:
            # Move data to the specified device
            x_seq, y_seq, target_y = x_seq.to(device), y_seq.to(device), target_y.to(device)

            # Reshape inputs as required and convert to NumPy arrays
            x_seq_np = x_seq.view(x_seq.size(0), -1).cpu().numpy().astype(np.float32)
            y_seq_np = y_seq.view(y_seq.size(0), -1).cpu().numpy().astype(np.float32)

            # Perform ONNX inference
            outputs = session.run([output_name], {input_x_name: x_seq_np, input_y_name: y_seq_np})
            outputs = outputs[0]  # Extract output array

            # Compute loss using the criterion
            outputs_tensor = torch.tensor(outputs, device=device)  # Convert ONNX output to PyTorch tensor
            loss = criterion(outputs_tensor, target_y)
            val_loss += loss.item()

            # Calculate residuals (difference between targets and predictions)
            residuals = (target_y.cpu().numpy() - outputs)

            # Collect residuals for each output
            for i in range(num_outputs):
                all_residuals[i].extend(residuals[:, i])

    return val_loss / len(val_loader), all_residuals


def check_gpu_status():
    """Check GPU temperature and power status"""
    if torch.cuda.is_available():
        # Get GPU utilization and memory
        memory_allocated = torch.cuda.memory_allocated() / 1024 ** 3
        memory_reserved = torch.cuda.memory_reserved() / 1024 ** 3
        print(f"GPU Memory: {memory_allocated:.2f}GB allocated, {memory_reserved:.2f}GB reserved")

        # Force garbage collection
        import gc
        gc.collect()
        torch.cuda.empty_cache()
def main():
    print(f"Current time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    # set_seed()
    # device = get_device()
    print(f"Current time: {time.strftime('%Y-%m-%d %H:%M:%S')}")

    # GPU Diagnostic - Add this section
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    print(f"CUDA version: {torch.version.cuda}")
    if torch.cuda.is_available():
        print(f"GPU count: {torch.cuda.device_count()}")
        print(f"GPU name: {torch.cuda.get_device_name(0)}")
        print(f"Current GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024 ** 3:.1f} GB")
    else:
        print("CUDA not available - PyTorch is using CPU only")

    set_seed()
    device = get_device()
    print(f"Device type: {device.type}")
    # Load data and prepare DataLoaders
    X_train, X_test, Y_train, Y_test = load_data()
    train_dataset = TimeSeriesDataset(X_train, Y_train, INPUT_LAG, OUTPUT_LAG)
    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=2,pin_memory=True,  # Faster GPU transfer
        persistent_workers=False) # Since num_workers=0)

    val_dataset = TimeSeriesDataset(X_test, Y_test, INPUT_LAG, OUTPUT_LAG)
    val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=4)

    input_size = INPUT_LAG * X_train.shape[2] + OUTPUT_LAG * Y_train.shape[2]
    output_size = Y_train.shape[2]
    model = FNN_NARX(input_size, HIDDEN_SIZE, output_size).to(device)

    criterion = nn.MSELoss()
    optimizer = optim.AdamW(model.parameters(), lr=LEARNING_RATE)

    # Train the model
    train_model(model, train_loader, criterion, optimizer, device)

    # Save the trained model
    torch.save(model.state_dict(), MODEL_SAVE_PATH)
    print(f"Model saved to {MODEL_SAVE_PATH}")
    print(f"Current time: {time.strftime('%Y-%m-%d %H:%M:%S')}")

    # model evaluation
#
# Example usage:
    loaded_model = FNN_NARX(input_size, HIDDEN_SIZE, output_size).to(device)

    loaded_model.load_state_dict(torch.load(MODEL_SAVE_PATH))

    # Prepare example inputs
    # x_example = torch.randn(1, input_size). to(device)
    # y_example = torch.randn(1, output_size).to(device)
    train_dataset = TimeSeriesDataset(X_train, Y_train, INPUT_LAG, OUTPUT_LAG)
    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)
    x_seq, y_seq, target_y = next(iter(train_loader))
    x_seq, y_seq, target_y = x_seq.to(device), y_seq.to(device), target_y.to(device)

    # Reshape inputs as required
    x_seq = x_seq.view(x_seq.size(0), -1)
    y_seq = y_seq.view(y_seq.size(0), -1)
    # Export to ONNX
    # onnx_file_path = "fnn_narx_model_20g.onnx"
    # onnx_file_path = "fnn_narx_model_20g_reduce_normalized.onnx"
    # onnx_file_path = "fnn_narx_model_test_normal.onnx"

    # export_model_to_onnx(loaded_model, x_seq, y_seq, onnx_file_path)
#
# # Load ONNX model
#     onnx_session = ort.InferenceSession("fnn_narx_model_old.onnx")
#     onnx_session = ort.InferenceSession("fnn_narx_model_20g.onnx", providers=['CUDAExecutionProvider'])
#     onnx_session = ort.InferenceSession("fnn_narx_model_test_normal.onnx", providers=['CUDAExecutionProvider'])

    # Example evaluation
    # val_loss, residuals = evaluate_onnx_model(onnx_session, val_loader, criterion, device)
    # val_loss, residuals = evaluate_onnx_model_per_channel(onnx_session, val_loader, criterion, device)

    # print("Validation Loss:", val_loss)
    # print("Residuals:", residuals)
    #
    # print(f"Validation Loss: {val_loss:.4f}")
    # # plot_losses(average_losses)
    # plot_residuals(residuals)
#
# ### compare with the pytorch model.
#     val_loss, all_residuals = evaluate_model_per_output(loaded_model, val_loader, criterion, device, num_outputs=OUTPUT_CHANNELS)
#     print(f"Validation Loss: {val_loss:.4f}")
#     # plot_losses(average_losses)
#     plot_residuals(all_residuals)




if __name__ == '__main__':
    # main()
    # main()
    save_console_output(f'console_output_{time.strftime("%Y%m%d_%H%M%S")}.txt', main)
