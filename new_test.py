import os
import numpy as np
import torch
import torch.nn as nn
import pickle
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import glob
import random

# === Configuration ===
MODEL_PATH = "FNN_NARX_eigenvalue_model.pth"
X_SCALER_PATH = "X_scaler.pkl"
Y_SCALER_PATH = "Y_scaler.pkl"
DATA_DIR = 'data/'

INPUT_LAG = 3
OUTPUT_LAG = 2
HIDDEN_SIZE = 128
OUTPUT_CHANNELS = 22

# Input and output columns (same as training)
INPUT_COLUMNS = list(range(2, 29))  # Columns 2-28 (27 features)
OUTPUT_COLUMNS = list(range(41, 63))  # Columns 41-62 (22 eigenvalue features)

# Eigenvalue names for plotting
EIGENVALUE_NAMES = []
for i in range(11):  # EV_0 to EV_10
    EIGENVALUE_NAMES.extend([f'EV_{i}_real', f'EV_{i}_imag'])


# === Model Definition ===
class FNN_NARX_Eigenvalue(nn.Module):
    """Feedforward Neural Network for NARX eigenvalue modeling."""

    def __init__(self, input_size, hidden_size, output_size):
        super(FNN_NARX_Eigenvalue, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, output_size)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.1)

    def forward(self, x, y):
        combined_input = torch.cat((x, y), dim=1)
        out = self.relu(self.fc1(combined_input))
        out = self.dropout(out)
        out = self.relu(self.fc2(out))
        out = self.dropout(out)
        out = self.fc3(out)
        return out


def load_model_and_scalers():
    """Load the trained model and scalers."""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # Load scalers
    with open(X_SCALER_PATH, 'rb') as f:
        X_scaler = pickle.load(f)
    with open(Y_SCALER_PATH, 'rb') as f:
        Y_scaler = pickle.load(f)

    # Calculate input size
    input_size = INPUT_LAG * len(INPUT_COLUMNS) + OUTPUT_LAG * len(OUTPUT_COLUMNS)

    # Load model
    model = FNN_NARX_Eigenvalue(input_size, HIDDEN_SIZE, OUTPUT_CHANNELS).to(device)
    model.load_state_dict(torch.load(MODEL_PATH, map_location=device))
    model.eval()

    return model, X_scaler, Y_scaler, device


def load_test_data(file_path):
    """Load and preprocess a single test file."""
    data = np.loadtxt(file_path, delimiter=',', skiprows=1)
    X = data[:, INPUT_COLUMNS]
    Y = data[:, OUTPUT_COLUMNS]
    return X.astype(np.float32), Y.astype(np.float32)


def predict_sequence(model, X_scaled, Y_scaled, X_scaler, Y_scaler, device):
    """Predict eigenvalues for a sequence."""
    predictions = []
    targets = []

    num_timesteps = X_scaled.shape[0]

    with torch.no_grad():
        for t in range(max(INPUT_LAG, OUTPUT_LAG), num_timesteps):
            # Extract input sequences
            x_seq = X_scaled[t - INPUT_LAG:t, :].reshape(-1)
            y_seq = Y_scaled[t - OUTPUT_LAG:t, :].reshape(-1)
            target_y = Y_scaled[t, :]

            # Convert to tensors
            x_seq = torch.tensor(x_seq, dtype=torch.float32).unsqueeze(0).to(device)
            y_seq = torch.tensor(y_seq, dtype=torch.float32).unsqueeze(0).to(device)

            # Predict
            pred = model(x_seq, y_seq)

            predictions.append(pred.cpu().numpy().flatten())
            targets.append(target_y)

    predictions = np.array(predictions)
    targets = np.array(targets)

    # Denormalize predictions and targets
    predictions_denorm = Y_scaler.inverse_transform(predictions)
    targets_denorm = Y_scaler.inverse_transform(targets)

    return predictions_denorm, targets_denorm


def plot_eigenvalue_comparison(predictions, targets, file_name, num_eigenvalues=6):
    """Plot actual vs predicted values for selected eigenvalues."""

    # Select eigenvalues to plot (mix of real and imaginary parts)
    eigenvalue_indices = [0, 1, 4, 5, 8, 9]  # EV_0_real, EV_0_imag, EV_2_real, EV_2_imag, EV_4_real, EV_4_imag

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'Eigenvalue Predictions vs Actual Values\nFile: {file_name}', fontsize=16, fontweight='bold')

    axes = axes.flatten()

    for i, ev_idx in enumerate(eigenvalue_indices):
        ax = axes[i]

        # Time steps
        time_steps = range(len(predictions))

        # Plot actual vs predicted
        ax.plot(time_steps, targets[:, ev_idx], 'b-', linewidth=2, label='Actual', alpha=0.8)
        ax.plot(time_steps, predictions[:, ev_idx], 'r--', linewidth=2, label='Predicted', alpha=0.8)

        # Calculate R² for this eigenvalue
        r2 = r2_score(targets[:, ev_idx], predictions[:, ev_idx])
        rmse = np.sqrt(mean_squared_error(targets[:, ev_idx], predictions[:, ev_idx]))

        # Formatting
        ax.set_title(f'{EIGENVALUE_NAMES[ev_idx]}\nR² = {r2:.4f}, RMSE = {rmse:.6f}', fontweight='bold')
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Eigenvalue')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # Color code performance
        if r2 > 0.8:
            ax.patch.set_facecolor('#e8f5e8')  # Light green for good performance
        elif r2 > 0.5:
            ax.patch.set_facecolor('#fff8dc')  # Light yellow for moderate performance
        else:
            ax.patch.set_facecolor('#ffe4e1')  # Light red for poor performance

    plt.tight_layout()

    # Save plot
    plot_filename = f"eigenvalue_comparison_{file_name.replace('.csv', '')}.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"Plot saved as: {plot_filename}")

    plt.show()


def plot_scatter_comparison(predictions, targets, file_name):
    """Create scatter plots showing correlation between actual and predicted values."""

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'Actual vs Predicted Scatter Plots\nFile: {file_name}', fontsize=16, fontweight='bold')

    # Select same eigenvalues as time series plot
    eigenvalue_indices = [0, 1, 4, 5, 8, 9]
    axes = axes.flatten()

    for i, ev_idx in enumerate(eigenvalue_indices):
        ax = axes[i]

        actual = targets[:, ev_idx]
        predicted = predictions[:, ev_idx]

        # Scatter plot
        ax.scatter(actual, predicted, alpha=0.6, s=20)

        # Perfect prediction line (y = x)
        min_val = min(actual.min(), predicted.min())
        max_val = max(actual.max(), predicted.max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')

        # Calculate metrics
        r2 = r2_score(actual, predicted)

        # Formatting
        ax.set_title(f'{EIGENVALUE_NAMES[ev_idx]}\nR² = {r2:.4f}', fontweight='bold')
        ax.set_xlabel('Actual Values')
        ax.set_ylabel('Predicted Values')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # Make axes equal for better comparison
        ax.set_aspect('equal', adjustable='box')

    plt.tight_layout()

    # Save plot
    plot_filename = f"scatter_comparison_{file_name.replace('.csv', '')}.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"Scatter plot saved as: {plot_filename}")

    plt.show()


def plot_performance_summary(all_r2_scores, file_names):
    """Plot performance summary across all tested files."""

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

    # Plot 1: R² scores distribution
    ax1.hist(all_r2_scores, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(np.mean(all_r2_scores), color='red', linestyle='--', linewidth=2,
                label=f'Mean = {np.mean(all_r2_scores):.3f}')
    ax1.axvline(np.median(all_r2_scores), color='green', linestyle='--', linewidth=2,
                label=f'Median = {np.median(all_r2_scores):.3f}')
    ax1.set_xlabel('R² Score')
    ax1.set_ylabel('Frequency')
    ax1.set_title('Distribution of R² Scores Across All Eigenvalues')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Plot 2: Performance categories
    excellent = sum(1 for r2 in all_r2_scores if r2 > 0.8)
    good = sum(1 for r2 in all_r2_scores if 0.5 < r2 <= 0.8)
    poor = sum(1 for r2 in all_r2_scores if 0 < r2 <= 0.5)
    very_poor = sum(1 for r2 in all_r2_scores if r2 <= 0)

    categories = ['Excellent\n(R² > 0.8)', 'Good\n(0.5 < R² ≤ 0.8)', 'Poor\n(0 < R² ≤ 0.5)', 'Very Poor\n(R² ≤ 0)']
    counts = [excellent, good, poor, very_poor]
    colors = ['green', 'yellow', 'orange', 'red']

    bars = ax2.bar(categories, counts, color=colors, alpha=0.7, edgecolor='black')
    ax2.set_ylabel('Number of Eigenvalues')
    ax2.set_title('Performance Categories')
    ax2.grid(True, alpha=0.3, axis='y')

    # Add count labels on bars
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width() / 2., height + 0.5, f'{count}',
                 ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()

    # Save plot
    plt.savefig("performance_summary.png", dpi=300, bbox_inches='tight')
    print("Performance summary saved as: performance_summary.png")

    plt.show()


def test_random_simulation():
    """Test the model on one random simulation with detailed visualization."""

    # Load model and scalers
    model, X_scaler, Y_scaler, device = load_model_and_scalers()

    # Get all CSV files
    csv_files = glob.glob(os.path.join(DATA_DIR, "*.csv"))
    if not csv_files:
        print(f"Error: No CSV files found in {DATA_DIR}")
        return

    # Select random file
    random_file = random.choice(csv_files)
    file_name = os.path.basename(random_file)

    print(f"\n=== Testing Random Simulation ===")
    print(f"Selected file: {file_name}")

    # Load and preprocess data
    X, Y = load_test_data(random_file)
    print(f"Data shape - X: {X.shape}, Y: {Y.shape}")

    # Normalize data
    X_flat = X.reshape(-1, X.shape[-1])
    Y_flat = Y.reshape(-1, Y.shape[-1])

    X_scaled_flat = X_scaler.transform(X_flat)
    Y_scaled_flat = Y_scaler.transform(Y_flat)

    X_scaled = X_scaled_flat.reshape(X.shape)
    Y_scaled = Y_scaled_flat.reshape(Y.shape)

    # Make predictions
    predictions, targets = predict_sequence(model, X_scaled, Y_scaled, X_scaler, Y_scaler, device)
    print(f"Generated {len(predictions)} predictions")

    # Calculate overall metrics
    overall_r2 = r2_score(targets, predictions)
    overall_rmse = np.sqrt(mean_squared_error(targets, predictions))
    overall_mae = mean_absolute_error(targets, predictions)

    print(f"\n=== Overall Performance ===")
    print(f"Overall R² Score: {overall_r2:.4f}")
    print(f"Overall RMSE: {overall_rmse:.6f}")
    print(f"Overall MAE: {overall_mae:.6f}")

    # Per-eigenvalue metrics
    per_eigenvalue_r2 = []
    for i in range(OUTPUT_CHANNELS):
        r2_i = r2_score(targets[:, i], predictions[:, i])
        per_eigenvalue_r2.append(r2_i)

    # Show best and worst performing eigenvalues
    best_idx = np.argmax(per_eigenvalue_r2)
    worst_idx = np.argmin(per_eigenvalue_r2)

    print(f"\nBest eigenvalue: {EIGENVALUE_NAMES[best_idx]} (R²: {per_eigenvalue_r2[best_idx]:.4f})")
    print(f"Worst eigenvalue: {EIGENVALUE_NAMES[worst_idx]} (R²: {per_eigenvalue_r2[worst_idx]:.4f})")

    # Create visualizations
    print("\n=== Creating Visualizations ===")
    plot_eigenvalue_comparison(predictions, targets, file_name)
    plot_scatter_comparison(predictions, targets, file_name)

    return predictions, targets, per_eigenvalue_r2, file_name


def test_multiple_simulations(num_files=3):
    """Test model on multiple simulations and create summary."""

    csv_files = glob.glob(os.path.join(DATA_DIR, "*.csv"))
    if not csv_files:
        print(f"Error: No CSV files found in {DATA_DIR}")
        return

    print(f"\n=== Testing Multiple Simulations ===")
    print(f"Testing on {min(num_files, len(csv_files))} files...")

    model, X_scaler, Y_scaler, device = load_model_and_scalers()

    all_r2_scores = []
    file_names = []

    for i, file_path in enumerate(csv_files[:num_files]):
        file_name = os.path.basename(file_path)
        file_names.append(file_name)

        try:
            print(f"\nTesting {file_name}...")

            # Load and process data
            X, Y = load_test_data(file_path)
            X_flat = X.reshape(-1, X.shape[-1])
            Y_flat = Y.reshape(-1, Y.shape[-1])
            X_scaled_flat = X_scaler.transform(X_flat)
            Y_scaled_flat = Y_scaler.transform(Y_flat)
            X_scaled = X_scaled_flat.reshape(X.shape)
            Y_scaled = Y_scaled_flat.reshape(Y.shape)

            # Predict
            predictions, targets = predict_sequence(model, X_scaled, Y_scaled, X_scaler, Y_scaler, device)

            # Calculate per-eigenvalue R² scores
            for j in range(OUTPUT_CHANNELS):
                r2_j = r2_score(targets[:, j], predictions[:, j])
                all_r2_scores.append(r2_j)

            # Overall performance for this file
            overall_r2 = r2_score(targets, predictions)
            print(f"  Overall R² for {file_name}: {overall_r2:.4f}")

        except Exception as e:
            print(f"Error testing {file_path}: {e}")

    if all_r2_scores:
        print(f"\n=== Multi-File Summary ===")
        print(f"Total eigenvalue predictions tested: {len(all_r2_scores)}")
        print(f"Average R² across all: {np.mean(all_r2_scores):.4f}")
        print(f"Median R² across all: {np.median(all_r2_scores):.4f}")
        print(f"Standard deviation: {np.std(all_r2_scores):.4f}")
        print(f"Min R²: {np.min(all_r2_scores):.4f}")
        print(f"Max R²: {np.max(all_r2_scores):.4f}")

        # Create performance summary plot
        plot_performance_summary(all_r2_scores, file_names)


def main():
    """Main testing function with comprehensive analysis."""
    print("=== Comprehensive Eigenvalue Model Testing ===")

    # Check if required files exist
    required_files = [MODEL_PATH, X_SCALER_PATH, Y_SCALER_PATH]
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"Error: Required file {file_path} not found!")
            print("Please run train_eigenvalue_model.py first.")
            return

    print("All required files found. Starting comprehensive testing...")

    # Test 1: Random simulation with detailed visualization
    print("\n" + "=" * 60)
    print("TEST 1: DETAILED ANALYSIS OF ONE RANDOM SIMULATION")
    print("=" * 60)

    predictions, targets, per_eigenvalue_r2, file_name = test_random_simulation()

    # Test 2: Multiple simulations summary
    print("\n" + "=" * 60)
    print("TEST 2: SUMMARY ACROSS MULTIPLE SIMULATIONS")
    print("=" * 60)

    test_multiple_simulations(num_files=3)

    print("\n" + "=" * 60)
    print("TESTING COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("Check the generated PNG files for detailed visualizations:")
    print("- eigenvalue_comparison_*.png: Time series comparisons")
    print("- scatter_comparison_*.png: Correlation analysis")
    print("- performance_summary.png: Overall performance analysis")


if __name__ == "__main__":
    main()