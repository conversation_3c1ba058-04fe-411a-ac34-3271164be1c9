import os
import numpy as np
import torch
import torch.nn as nn
import pickle
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import glob

# === Configuration ===
MODEL_PATH = "FNN_NARX_eigenvalue_model.pth"
X_SCALER_PATH = "X_scaler.pkl"
Y_SCALER_PATH = "Y_scaler.pkl"
DATA_DIR = 'data/'

INPUT_LAG = 3
OUTPUT_LAG = 2
HIDDEN_SIZE = 128
OUTPUT_CHANNELS = 22

# Input and output columns (same as training)
INPUT_COLUMNS = list(range(2, 29))  # Columns 2-28 (27 features)
OUTPUT_COLUMNS = list(range(41, 63))  # Columns 41-62 (22 eigenvalue features)

# === Model Definition (same as training) ===
class FNN_NARX_Eigenvalue(nn.Module):
    """Feedforward Neural Network for NARX eigenvalue modeling."""
    def __init__(self, input_size, hidden_size, output_size):
        super(FNN_NARX_Eigenvalue, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, output_size)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.1)

    def forward(self, x, y):
        combined_input = torch.cat((x, y), dim=1)
        out = self.relu(self.fc1(combined_input))
        out = self.dropout(out)
        out = self.relu(self.fc2(out))
        out = self.dropout(out)
        out = self.fc3(out)
        return out

def load_model_and_scalers():
    """Load the trained model and scalers."""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Load scalers
    with open(X_SCALER_PATH, 'rb') as f:
        X_scaler = pickle.load(f)
    with open(Y_SCALER_PATH, 'rb') as f:
        Y_scaler = pickle.load(f)
    
    # Calculate input size
    input_size = INPUT_LAG * len(INPUT_COLUMNS) + OUTPUT_LAG * len(OUTPUT_COLUMNS)
    
    # Load model
    model = FNN_NARX_Eigenvalue(input_size, HIDDEN_SIZE, OUTPUT_CHANNELS).to(device)
    model.load_state_dict(torch.load(MODEL_PATH, map_location=device))
    model.eval()
    
    return model, X_scaler, Y_scaler, device

def load_test_data(file_path):
    """Load and preprocess a single test file."""
    data = np.loadtxt(file_path, delimiter=',', skiprows=1)
    X = data[:, INPUT_COLUMNS]
    Y = data[:, OUTPUT_COLUMNS]
    return X.astype(np.float32), Y.astype(np.float32)

def predict_sequence(model, X_scaled, Y_scaled, X_scaler, Y_scaler, device):
    """Predict eigenvalues for a sequence."""
    predictions = []
    targets = []
    
    num_timesteps = X_scaled.shape[0]
    
    with torch.no_grad():
        for t in range(max(INPUT_LAG, OUTPUT_LAG), num_timesteps):
            # Extract input sequences
            x_seq = X_scaled[t - INPUT_LAG:t, :].reshape(-1)
            y_seq = Y_scaled[t - OUTPUT_LAG:t, :].reshape(-1)
            target_y = Y_scaled[t, :]
            
            # Convert to tensors
            x_seq = torch.tensor(x_seq, dtype=torch.float32).unsqueeze(0).to(device)
            y_seq = torch.tensor(y_seq, dtype=torch.float32).unsqueeze(0).to(device)
            
            # Predict
            pred = model(x_seq, y_seq)
            
            predictions.append(pred.cpu().numpy().flatten())
            targets.append(target_y)
    
    predictions = np.array(predictions)
    targets = np.array(targets)
    
    # Denormalize predictions and targets
    predictions_denorm = Y_scaler.inverse_transform(predictions)
    targets_denorm = Y_scaler.inverse_transform(targets)
    
    return predictions_denorm, targets_denorm

def evaluate_predictions(predictions, targets):
    """Evaluate prediction quality."""
    mse = mean_squared_error(targets, predictions)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(targets, predictions)
    r2 = r2_score(targets, predictions)
    
    # Per-output metrics
    per_output_r2 = []
    per_output_rmse = []
    per_output_mae = []
    
    for i in range(predictions.shape[1]):
        r2_i = r2_score(targets[:, i], predictions[:, i])
        rmse_i = np.sqrt(mean_squared_error(targets[:, i], predictions[:, i]))
        mae_i = mean_absolute_error(targets[:, i], predictions[:, i])
        per_output_r2.append(r2_i)
        per_output_rmse.append(rmse_i)
        per_output_mae.append(mae_i)
    
    return {
        'mse': mse,
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'per_output_r2': per_output_r2,
        'per_output_rmse': per_output_rmse,
        'per_output_mae': per_output_mae
    }

def print_detailed_results(metrics):
    """Print detailed results for each eigenvalue."""
    print("\n=== Detailed Results per Eigenvalue ===")
    print(f"{'Eigenvalue':<15} {'R²':<8} {'RMSE':<10} {'MAE':<10}")
    print("-" * 50)
    
    for i in range(OUTPUT_CHANNELS):
        ev_num = i // 2
        part = "real" if i % 2 == 0 else "imag"
        ev_name = f"EV_{ev_num}_{part}"
        
        print(f"{ev_name:<15} {metrics['per_output_r2'][i]:<8.4f} {metrics['per_output_rmse'][i]:<10.6f} {metrics['per_output_mae'][i]:<10.6f}")

def test_single_file(file_path):
    """Test the model on a single file."""
    print(f"\n=== Testing on {os.path.basename(file_path)} ===")
    
    # Load model and scalers
    model, X_scaler, Y_scaler, device = load_model_and_scalers()
    
    # Load test data
    X, Y = load_test_data(file_path)
    print(f"Loaded data shape - X: {X.shape}, Y: {Y.shape}")
    
    # Normalize data
    X_flat = X.reshape(-1, X.shape[-1])
    Y_flat = Y.reshape(-1, Y.shape[-1])
    
    X_scaled_flat = X_scaler.transform(X_flat)
    Y_scaled_flat = Y_scaler.transform(Y_flat)
    
    X_scaled = X_scaled_flat.reshape(X.shape)
    Y_scaled = Y_scaled_flat.reshape(Y.shape)
    
    # Make predictions
    predictions, targets = predict_sequence(model, X_scaled, Y_scaled, X_scaler, Y_scaler, device)
    print(f"Generated {len(predictions)} predictions")
    
    # Evaluate
    metrics = evaluate_predictions(predictions, targets)
    
    print(f"\n=== Overall Performance ===")
    print(f"Overall R² Score: {metrics['r2']:.4f}")
    print(f"Overall RMSE: {metrics['rmse']:.6f}")
    print(f"Overall MAE: {metrics['mae']:.6f}")
    
    # Show best and worst performing eigenvalues
    best_idx = np.argmax(metrics['per_output_r2'])
    worst_idx = np.argmin(metrics['per_output_r2'])
    
    print(f"\nBest performing eigenvalue: EV_{best_idx//2}_{'real' if best_idx%2==0 else 'imag'} (R²: {metrics['per_output_r2'][best_idx]:.4f})")
    print(f"Worst performing eigenvalue: EV_{worst_idx//2}_{'real' if worst_idx%2==0 else 'imag'} (R²: {metrics['per_output_r2'][worst_idx]:.4f})")
    
    # Print detailed results
    print_detailed_results(metrics)
    
    # Summary statistics
    r2_scores = metrics['per_output_r2']
    print(f"\n=== Summary Statistics ===")
    print(f"Average R² across all eigenvalues: {np.mean(r2_scores):.4f}")
    print(f"Median R² across all eigenvalues: {np.median(r2_scores):.4f}")
    print(f"Number of eigenvalues with R² > 0.8: {sum(1 for r2 in r2_scores if r2 > 0.8)}")
    print(f"Number of eigenvalues with R² > 0.5: {sum(1 for r2 in r2_scores if r2 > 0.5)}")
    print(f"Number of eigenvalues with R² < 0: {sum(1 for r2 in r2_scores if r2 < 0)}")
    
    return predictions, targets, metrics

def test_multiple_files(num_files=3):
    """Test the model on multiple files."""
    csv_files = glob.glob(os.path.join(DATA_DIR, "*.csv"))
    if not csv_files:
        print(f"Error: No CSV files found in {DATA_DIR}")
        return
    
    print(f"Testing on {min(num_files, len(csv_files))} files...")
    
    all_r2_scores = []
    
    for i, file_path in enumerate(csv_files[:num_files]):
        try:
            _, _, metrics = test_single_file(file_path)
            all_r2_scores.extend(metrics['per_output_r2'])
        except Exception as e:
            print(f"Error testing {file_path}: {e}")
    
    if all_r2_scores:
        print(f"\n=== Multi-File Summary ===")
        print(f"Average R² across all files and eigenvalues: {np.mean(all_r2_scores):.4f}")
        print(f"Median R² across all files and eigenvalues: {np.median(all_r2_scores):.4f}")
        print(f"Standard deviation of R² scores: {np.std(all_r2_scores):.4f}")

def main():
    """Main testing function."""
    print("=== Eigenvalue Prediction Model Testing ===")
    
    # Check if model files exist
    if not os.path.exists(MODEL_PATH):
        print(f"Error: Model file {MODEL_PATH} not found!")
        return
    
    if not os.path.exists(X_SCALER_PATH) or not os.path.exists(Y_SCALER_PATH):
        print("Error: Scaler files not found!")
        return
    
    print("All required files found. Starting tests...")
    
    # Test on multiple files
    test_multiple_files(num_files=3)
    
    print("\nTesting completed successfully!")

if __name__ == "__main__":
    main()
