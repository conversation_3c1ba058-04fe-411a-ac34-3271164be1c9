import os
import platform
import random
import time
import winsound
import sys
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import train_test_split
from torch.utils.data import Dataset, DataLoader
import traceback
from sklearn.preprocessing import MinMaxScaler
from torch.amp import autocast, GradScaler
import glob


# === Global Config ===
SEED = 42
DATA_DIR = 'data/'
MODEL_SAVE_PATH = "FNN_NARX_eigenvalue_model.pth"

INPUT_LAG = 5
OUTPUT_LAG = 3
HIDDEN_SIZE = 64  # Increased for more complex eigenvalue prediction
BATCH_SIZE = 512
EPOCHS = 15
LEARNING_RATE = 0.001
NOISE_RATIO = 0
OUTPUT_CHANNELS = 22  # 22 eigenvalue outputs (columns 41-62)

# Input columns: 2-28 (27 features)
INPUT_COLUMNS = list(range(2, 29))
# Output columns: 41-62 (22 eigenvalue features)  
OUTPUT_COLUMNS = list(range(41, 63))

## GPU status check
def check_gpu_status():
    """Check GPU temperature and power status"""
    if torch.cuda.is_available():
        memory_allocated = torch.cuda.memory_allocated() / 1024 ** 3
        memory_reserved = torch.cuda.memory_reserved() / 1024 ** 3
        print(f"GPU Memory: {memory_allocated:.2f}GB allocated, {memory_reserved:.2f}GB reserved")
        
        import gc
        gc.collect()
        torch.cuda.empty_cache()

# === Console output saving ===
class DualOutput:
    def __init__(self, file_handle):
        self.file = file_handle
        self.console = sys.__stdout__

    def write(self, text):
        self.console.write(text)
        self.file.write(text)
        self.console.flush()
        self.file.flush()

    def flush(self):
        self.console.flush()
        self.file.flush()

def save_console_output(file_path, func, *args, **kwargs):
    original_stdout = sys.stdout
    print(f"Executing file: {os.path.abspath(__file__)}")
    
    with open(file_path, 'w', encoding='utf-8') as f:
        dual_output = DualOutput(f)
        sys.stdout = dual_output
        
        print(f"Executing file: {os.path.abspath(__file__)}")
        
        try:
            func(*args, **kwargs)
        except Exception as e:
            traceback.print_exc()
    
    sys.stdout = original_stdout

# === Utility Functions ===
def set_seed(seed=SEED):
    """Sets random seeds for reproducibility."""
    torch.manual_seed(seed)
    np.random.seed(seed)
    random.seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)

def get_device():
    """Detects the device and sets CPU/GPU configurations."""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    if device.type == 'cpu' and platform.machine() in ['x86_64', 'arm64']:
        torch.set_num_threads(os.cpu_count() - (2 if platform.machine() == 'x86_64' else 0))
    return device

# === Data Loading & Processing ===
def load_data(data_dir=DATA_DIR):
    """Loads and preprocesses the eigenvalue dataset."""
    X_data, Y_data = [], []
    
    # Get all CSV files in the data directory
    csv_files = glob.glob(os.path.join(data_dir, "*.csv"))
    print(f"Found {len(csv_files)} CSV files")
    
    for csv_file in csv_files:
        try:
            data = np.loadtxt(csv_file, delimiter=',', skiprows=1)
            
            # Extract input features (columns 2-28)
            X = data[:, INPUT_COLUMNS]
            # Extract output features (columns 41-62) 
            Y = data[:, OUTPUT_COLUMNS]
            
            X_data.append(X)
            Y_data.append(Y)
            
        except Exception as e:
            print(f"Error loading {csv_file}: {e}")
            continue
    
    X_data = np.array(X_data, dtype=np.float32)
    Y_data = np.array(Y_data, dtype=np.float32)
    
    print(f"Loaded data shape - X: {X_data.shape}, Y: {Y_data.shape}")
    
    return train_test_split(X_data, Y_data, test_size=0.2, random_state=SEED)

class TimeSeriesDataset(Dataset):
    """Custom Dataset for Time-Series Eigenvalue Data."""
    def __init__(self, X_data, Y_data, input_lag, output_lag, noise_ratio=NOISE_RATIO):
        self.X_data = X_data
        self.Y_data = Y_data
        self.input_lag = input_lag
        self.output_lag = output_lag
        self.noise_ratio = noise_ratio
        self.num_timesteps = X_data.shape[1]
        self.num_samples = X_data.shape[0] * (self.num_timesteps - max(input_lag, output_lag))

    def __len__(self):
        return self.num_samples

    def __getitem__(self, idx):
        experiment_idx = idx // (self.num_timesteps - max(self.input_lag, self.output_lag))
        time_idx = idx % (self.num_timesteps - max(self.input_lag, self.output_lag)) + max(self.input_lag, self.output_lag)

        # Extract sequences
        x_seq = self.X_data[experiment_idx, time_idx - self.input_lag:time_idx, :]
        y_seq = self.Y_data[experiment_idx, time_idx - self.output_lag:time_idx, :]
        target_y = self.Y_data[experiment_idx, time_idx, :]

        # Convert to PyTorch tensors
        x_seq = torch.tensor(x_seq, dtype=torch.float32)
        y_seq = torch.tensor(y_seq, dtype=torch.float32)
        target_y = torch.tensor(target_y, dtype=torch.float32)

        return x_seq.reshape(-1), y_seq.reshape(-1), target_y

# === Model Definition ===
class FNN_NARX_Eigenvalue(nn.Module):
    """Feedforward Neural Network for NARX eigenvalue modeling."""
    def __init__(self, input_size, hidden_size, output_size):
        super(FNN_NARX_Eigenvalue, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)  # Additional layer for complexity
        self.fc3 = nn.Linear(hidden_size, output_size)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.1)  # Regularization

    def forward(self, x, y):
        combined_input = torch.cat((x, y), dim=1)
        out = self.relu(self.fc1(combined_input))
        out = self.dropout(out)
        out = self.relu(self.fc2(out))
        out = self.dropout(out)
        out = self.fc3(out)
        return out

def train_model(model, train_loader, criterion, optimizer, device, epochs=EPOCHS):
    """Trains the model with mixed precision using the given DataLoader."""
    model.train()
    scaler = GradScaler('cuda') if device.type == 'cuda' else None

    for epoch in range(epochs):
        check_gpu_status()
        
        epoch_loss = 0
        all_predictions = []
        all_targets = []
        start_time = time.time()

        for x_seq, y_seq, target_y in train_loader:
            x_seq, y_seq, target_y = x_seq.to(device), y_seq.to(device), target_y.to(device)

            # Forward pass under mixed precision
            if device.type == 'cuda':
                with autocast('cuda'):
                    outputs = model(x_seq, y_seq)
                    loss = criterion(outputs, target_y)
            else:
                outputs = model(x_seq, y_seq)
                loss = criterion(outputs, target_y)

            optimizer.zero_grad()

            # Backward pass and gradient scaling
            if device.type == 'cuda' and scaler is not None:
                scaler.scale(loss).backward()
                scaler.step(optimizer)
                scaler.update()
            else:
                loss.backward()
                optimizer.step()

            epoch_loss += loss.item()
            all_predictions.append(outputs.detach().cpu().numpy())
            all_targets.append(target_y.detach().cpu().numpy())

        # End time for the epoch
        end_time = time.time()

        # Concatenate all predictions and targets
        all_predictions = np.concatenate(all_predictions, axis=0)
        all_targets = np.concatenate(all_targets, axis=0)

        # Compute additional metrics
        mse = mean_squared_error(all_targets, all_predictions)
        rmse = np.sqrt(mse)
        r2 = r2_score(all_targets, all_predictions)
        fit_loss = np.mean(np.abs(all_targets - all_predictions) / (np.abs(all_targets) + 1e-8))

        # Print metrics
        print(f"Epoch completed in {(end_time - start_time):.2f} seconds")
        print(f"Loss (MSE): {epoch_loss / len(train_loader):.7f}")
        print(f"RMSE: {rmse:.7f}")
        print(f"R²: {r2:.7f}")
        print(f"Fit Loss: {fit_loss:.7f}")
        print(f"Epoch [{epoch + 1}/{epochs}] Loss: {epoch_loss / len(train_loader):.7f}")

def evaluate_model_per_output(model, val_loader, criterion, device, num_outputs=OUTPUT_CHANNELS):
    model.eval()
    val_loss = 0.0
    all_residuals = [[] for _ in range(num_outputs)]

    with torch.no_grad():
        for x_seq, y_seq, target_y in val_loader:
            x_seq, y_seq, target_y = x_seq.to(device), y_seq.to(device), target_y.to(device)

            x_seq = x_seq.view(x_seq.size(0), -1)
            y_seq = y_seq.view(y_seq.size(0), -1)

            outputs = model(x_seq, y_seq)
            loss = criterion(outputs, target_y)
            val_loss += loss.item()

            residuals = (target_y - outputs).cpu().numpy()

            for i in range(num_outputs):
                all_residuals[i].extend(residuals[:, i])

    return val_loss / len(val_loader), all_residuals

def plot_residuals(all_residuals):
    """Plot histograms of residuals for each eigenvalue output."""
    num_outputs = len(all_residuals)
    fig, axes = plt.subplots(4, 6, figsize=(20, 12), sharey=True)  # 4x6 grid for 22 outputs
    axes = axes.flatten()

    for i in range(num_outputs):
        axes[i].hist(all_residuals[i], bins=20, alpha=0.7, color='blue', edgecolor='black')
        axes[i].set_title(f'EV_{i//2}_{"real" if i%2==0 else "imag"}')
        axes[i].set_xlabel('Residual')
        if i % 6 == 0:  # Only leftmost plots get y-label
            axes[i].set_ylabel('Frequency')

    # Hide unused subplots
    for i in range(num_outputs, len(axes)):
        axes[i].set_visible(False)

    plt.tight_layout()
    plt.show()

def main():
    print(f"Current time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # GPU Diagnostic
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    print(f"CUDA version: {torch.version.cuda}")
    if torch.cuda.is_available():
        print(f"GPU count: {torch.cuda.device_count()}")
        print(f"GPU name: {torch.cuda.get_device_name(0)}")
        print(f"Current GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024 ** 3:.1f} GB")
    else:
        print("CUDA not available - PyTorch is using CPU only")

    set_seed()
    device = get_device()
    print(f"Device type: {device.type}")

    # Load data and prepare DataLoaders
    X_train, X_test, Y_train, Y_test = load_data()

    train_dataset = TimeSeriesDataset(X_train, Y_train, INPUT_LAG, OUTPUT_LAG)
    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, 
                             num_workers=4, pin_memory=True, persistent_workers=True)

    val_dataset = TimeSeriesDataset(X_test, Y_test, INPUT_LAG, OUTPUT_LAG)
    val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False,
                           num_workers=4, pin_memory=True, persistent_workers=True)

    input_size = INPUT_LAG * X_train.shape[2] + OUTPUT_LAG * Y_train.shape[2]
    output_size = Y_train.shape[2]
    model = FNN_NARX_Eigenvalue(input_size, HIDDEN_SIZE, output_size).to(device)

    criterion = nn.MSELoss()
    optimizer = optim.AdamW(model.parameters(), lr=LEARNING_RATE)

    # Train the model
    train_model(model, train_loader, criterion, optimizer, device)

    # Save the trained model
    torch.save(model.state_dict(), MODEL_SAVE_PATH)
    print(f"Model saved to {MODEL_SAVE_PATH}")

    # Model evaluation
    loaded_model = FNN_NARX_Eigenvalue(input_size, HIDDEN_SIZE, output_size).to(device)
    loaded_model.load_state_dict(torch.load(MODEL_SAVE_PATH))

    val_loss, all_residuals = evaluate_model_per_output(loaded_model, val_loader, criterion, device, num_outputs=OUTPUT_CHANNELS)
    print(f"Validation Loss: {val_loss:.4f}")
    plot_residuals(all_residuals)

if __name__ == '__main__':
    save_console_output(f'eigenvalue_training_output_{time.strftime("%Y%m%d_%H%M%S")}.txt', main)
