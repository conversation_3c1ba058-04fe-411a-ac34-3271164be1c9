import os
import numpy as np
import torch
import torch.nn as nn
import pickle
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import glob
import random

# === Configuration ===
MODEL_PATH = "FNN_NARX_eigenvalue_model.pth"
X_SCALER_PATH = "X_scaler.pkl"
Y_SCALER_PATH = "Y_scaler.pkl"
DATA_DIR = 'data/'

INPUT_LAG = 3
OUTPUT_LAG = 2
HIDDEN_SIZE = 128
OUTPUT_CHANNELS = 22

# Input and output columns (same as training)
INPUT_COLUMNS = list(range(2, 29))  # Columns 2-28 (27 features)
OUTPUT_COLUMNS = list(range(41, 63))  # Columns 41-62 (22 eigenvalue features)

# Eigenvalue names for plotting
EIGENVALUE_NAMES = []
for i in range(11):  # EV_0 to EV_10
    EIGENVALUE_NAMES.extend([f'EV_{i}_real', f'EV_{i}_imag'])


# === Model Definition ===
class FNN_NARX_Eigenvalue(nn.Module):
    """Feedforward Neural Network for NARX eigenvalue modeling."""

    def __init__(self, input_size, hidden_size, output_size):
        super(FNN_NARX_Eigenvalue, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, output_size)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.1)

    def forward(self, x, y):
        combined_input = torch.cat((x, y), dim=1)
        out = self.relu(self.fc1(combined_input))
        out = self.dropout(out)
        out = self.relu(self.fc2(out))
        out = self.dropout(out)
        out = self.fc3(out)
        return out


def load_model_and_scalers():
    """Load the trained model and scalers."""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # Load scalers
    with open(X_SCALER_PATH, 'rb') as f:
        X_scaler = pickle.load(f)
    with open(Y_SCALER_PATH, 'rb') as f:
        Y_scaler = pickle.load(f)

    # Calculate input size
    input_size = INPUT_LAG * len(INPUT_COLUMNS) + OUTPUT_LAG * len(OUTPUT_COLUMNS)

    # Load model
    model = FNN_NARX_Eigenvalue(input_size, HIDDEN_SIZE, OUTPUT_CHANNELS).to(device)
    model.load_state_dict(torch.load(MODEL_PATH, map_location=device))
    model.eval()

    print(f"Model loaded on {device}, Input size: {input_size}")
    return model, X_scaler, Y_scaler, device


def load_test_data(file_path):
    """Load and preprocess a single test file."""
    data = np.loadtxt(file_path, delimiter=',', skiprows=1)
    X = data[:, INPUT_COLUMNS]
    Y = data[:, OUTPUT_COLUMNS]

    print(f"Data loaded - X shape: {X.shape}, Y shape: {Y.shape}")
    print(f"X range: [{np.min(X):.6f}, {np.max(X):.6f}]")
    print(f"Y range: [{np.min(Y):.6f}, {np.max(Y):.6f}]")

    return X.astype(np.float32), Y.astype(np.float32)


def predict_sequence(model, X_scaled, Y_scaled, X_scaler, Y_scaler, device):
    """Predict eigenvalues for a sequence."""
    predictions = []
    targets = []

    num_timesteps = X_scaled.shape[0]
    start_idx = max(INPUT_LAG, OUTPUT_LAG)

    print(f"Predicting from timestep {start_idx} to {num_timesteps - 1}")

    with torch.no_grad():
        for t in range(start_idx, num_timesteps):
            # Extract input sequences
            x_seq = X_scaled[t - INPUT_LAG:t, :].reshape(-1)
            y_seq = Y_scaled[t - OUTPUT_LAG:t, :].reshape(-1)
            target_y = Y_scaled[t, :]

            # Convert to tensors
            x_seq = torch.tensor(x_seq, dtype=torch.float32).unsqueeze(0).to(device)
            y_seq = torch.tensor(y_seq, dtype=torch.float32).unsqueeze(0).to(device)

            # Predict
            pred = model(x_seq, y_seq)

            predictions.append(pred.cpu().numpy().flatten())
            targets.append(target_y)

    predictions = np.array(predictions)
    targets = np.array(targets)

    # Denormalize predictions and targets
    predictions_denorm = Y_scaler.inverse_transform(predictions)
    targets_denorm = Y_scaler.inverse_transform(targets)

    print(f"Generated predictions shape: {predictions_denorm.shape}")
    print(f"Targets shape: {targets_denorm.shape}")

    return predictions_denorm, targets_denorm


def analyze_prediction_quality(predictions, targets):
    """Detailed analysis of prediction quality."""
    print("\n=== PREDICTION QUALITY ANALYSIS ===")

    # Overall metrics
    overall_r2 = r2_score(targets, predictions)
    overall_rmse = np.sqrt(mean_squared_error(targets, predictions))
    overall_mae = mean_absolute_error(targets, predictions)

    print(f"Overall R²: {overall_r2:.4f}")
    print(f"Overall RMSE: {overall_rmse:.6f}")
    print(f"Overall MAE: {overall_mae:.6f}")

    # Per-eigenvalue analysis
    per_ev_metrics = []
    for i in range(OUTPUT_CHANNELS):
        r2_i = r2_score(targets[:, i], predictions[:, i])
        rmse_i = np.sqrt(mean_squared_error(targets[:, i], predictions[:, i]))
        mae_i = mean_absolute_error(targets[:, i], predictions[:, i])

        per_ev_metrics.append({
            'name': EIGENVALUE_NAMES[i],
            'r2': r2_i,
            'rmse': rmse_i,
            'mae': mae_i,
            'target_std': np.std(targets[:, i]),
            'pred_std': np.std(predictions[:, i])
        })

    # Sort by R² score
    per_ev_metrics.sort(key=lambda x: x['r2'], reverse=True)

    print(f"\n=== TOP 5 BEST PERFORMING EIGENVALUES ===")
    for i, metric in enumerate(per_ev_metrics[:5]):
        print(f"{i + 1}. {metric['name']}: R²={metric['r2']:.4f}, RMSE={metric['rmse']:.6f}")

    print(f"\n=== TOP 5 WORST PERFORMING EIGENVALUES ===")
    for i, metric in enumerate(per_ev_metrics[-5:]):
        print(f"{i + 1}. {metric['name']}: R²={metric['r2']:.4f}, RMSE={metric['rmse']:.6f}")

    # Performance categories
    excellent = [m for m in per_ev_metrics if m['r2'] > 0.8]
    good = [m for m in per_ev_metrics if 0.5 < m['r2'] <= 0.8]
    poor = [m for m in per_ev_metrics if 0 < m['r2'] <= 0.5]
    very_poor = [m for m in per_ev_metrics if m['r2'] <= 0]

    print(f"\n=== PERFORMANCE CATEGORIES ===")
    print(f"Excellent (R² > 0.8): {len(excellent)} eigenvalues")
    print(f"Good (0.5 < R² ≤ 0.8): {len(good)} eigenvalues")
    print(f"Poor (0 < R² ≤ 0.5): {len(poor)} eigenvalues")
    print(f"Very Poor (R² ≤ 0): {len(very_poor)} eigenvalues")

    if len(very_poor) > 0:
        print(f"\nVery poor eigenvalues:")
        for m in very_poor:
            print(f"  - {m['name']}: R²={m['r2']:.4f}")

    return per_ev_metrics


def plot_eigenvalue_comparison(predictions, targets, file_name, num_eigenvalues=6):
    """Plot actual vs predicted values for selected eigenvalues."""

    # Calculate R² for all eigenvalues to select interesting ones
    r2_scores = [r2_score(targets[:, i], predictions[:, i]) for i in range(OUTPUT_CHANNELS)]

    # Select mix of best, worst, and medium performing eigenvalues
    sorted_indices = np.argsort(r2_scores)
    selected_indices = [
        sorted_indices[-1],  # Best
        sorted_indices[-2],  # Second best
        sorted_indices[len(sorted_indices) // 2],  # Median
        sorted_indices[len(sorted_indices) // 2 - 1],  # Near median
        sorted_indices[1],  # Second worst
        sorted_indices[0]  # Worst
    ]

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'Eigenvalue Predictions vs Actual Values\nFile: {file_name}', fontsize=16, fontweight='bold')

    axes = axes.flatten()

    for i, ev_idx in enumerate(selected_indices):
        ax = axes[i]

        # Time steps
        time_steps = range(len(predictions))

        # Plot actual vs predicted
        ax.plot(time_steps, targets[:, ev_idx], 'b-', linewidth=2, label='Actual', alpha=0.8)
        ax.plot(time_steps, predictions[:, ev_idx], 'r--', linewidth=2, label='Predicted', alpha=0.8)

        # Calculate metrics for this eigenvalue
        r2 = r2_scores[ev_idx]
        rmse = np.sqrt(mean_squared_error(targets[:, ev_idx], predictions[:, ev_idx]))

        # Color code performance
        if r2 > 0.8:
            ax.patch.set_facecolor('lightgreen')
            ax.patch.set_alpha(0.1)
        elif r2 > 0.5:
            ax.patch.set_facecolor('yellow')
            ax.patch.set_alpha(0.1)
        else:
            ax.patch.set_facecolor('lightcoral')
            ax.patch.set_alpha(0.1)

        # Formatting
        ax.set_title(f'{EIGENVALUE_NAMES[ev_idx]}\nR² = {r2:.4f}, RMSE = {rmse:.6f}', fontweight='bold')
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Eigenvalue')
        ax.legend()
        ax.grid(True, alpha=0.3)

    plt.tight_layout()

    # Save plot
    plot_filename = f"eigenvalue_comparison_{file_name.replace('.csv', '')}.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"Plot saved as: {plot_filename}")

    plt.show()


def plot_error_analysis(predictions, targets, file_name):
    """Create error analysis plots."""

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'Error Analysis\nFile: {file_name}', fontsize=16, fontweight='bold')

    # Plot 1: Error distribution
    errors = predictions - targets
    ax1 = axes[0, 0]
    ax1.hist(errors.flatten(), bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_title('Prediction Error Distribution')
    ax1.set_xlabel('Error (Predicted - Actual)')
    ax1.set_ylabel('Frequency')
    ax1.axvline(0, color='red', linestyle='--', linewidth=2)
    ax1.grid(True, alpha=0.3)

    # Plot 2: Error vs actual values
    ax2 = axes[0, 1]
    ax2.scatter(targets.flatten(), errors.flatten(), alpha=0.3, s=1)
    ax2.set_title('Error vs Actual Values')
    ax2.set_xlabel('Actual Values')
    ax2.set_ylabel('Error')
    ax2.axhline(0, color='red', linestyle='--', linewidth=2)
    ax2.grid(True, alpha=0.3)

    # Plot 3: R² scores per eigenvalue
    r2_scores = [r2_score(targets[:, i], predictions[:, i]) for i in range(OUTPUT_CHANNELS)]
    ax3 = axes[1, 0]
    bars = ax3.bar(range(len(r2_scores)), r2_scores,
                   color=['green' if r2 > 0.8 else 'yellow' if r2 > 0.5 else 'red' for r2 in r2_scores])
    ax3.set_title('R² Score per Eigenvalue')
    ax3.set_xlabel('Eigenvalue Index')
    ax3.set_ylabel('R² Score')
    ax3.axhline(0, color='black', linestyle='-', linewidth=1)
    ax3.axhline(0.5, color='orange', linestyle='--', linewidth=1, label='Good threshold')
    ax3.axhline(0.8, color='green', linestyle='--', linewidth=1, label='Excellent threshold')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # Plot 4: Prediction vs actual scatter (overall)
    ax4 = axes[1, 1]
    ax4.scatter(targets.flatten(), predictions.flatten(), alpha=0.3, s=1)
    min_val = min(targets.min(), predictions.min())
    max_val = max(targets.max(), predictions.max())
    ax4.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
    ax4.set_title('Overall Predictions vs Actual')
    ax4.set_xlabel('Actual Values')
    ax4.set_ylabel('Predicted Values')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()

    # Save plot
    plot_filename = f"error_analysis_{file_name.replace('.csv', '')}.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"Error analysis saved as: {plot_filename}")

    plt.show()


def test_random_simulation():
    """Test the model on one random simulation with detailed visualization."""

    # Load model and scalers
    model, X_scaler, Y_scaler, device = load_model_and_scalers()

    # Get all CSV files
    csv_files = glob.glob(os.path.join(DATA_DIR, "*.csv"))
    if not csv_files:
        print(f"Error: No CSV files found in {DATA_DIR}")
        return None, None, None, None

    # Select random file
    random_file = random.choice(csv_files)
    file_name = os.path.basename(random_file)

    print(f"\n=== Testing Random Simulation ===")
    print(f"Selected file: {file_name}")

    # Load and preprocess data
    X, Y = load_test_data(random_file)

    # Normalize data
    X_flat = X.reshape(-1, X.shape[-1])
    Y_flat = Y.reshape(-1, Y.shape[-1])

    X_scaled_flat = X_scaler.transform(X_flat)
    Y_scaled_flat = Y_scaler.transform(Y_flat)

    X_scaled = X_scaled_flat.reshape(X.shape)
    Y_scaled = Y_scaled_flat.reshape(Y.shape)

    # Make predictions
    predictions, targets = predict_sequence(model, X_scaled, Y_scaled, X_scaler, Y_scaler, device)

    # Analyze prediction quality
    per_eigenvalue_metrics = analyze_prediction_quality(predictions, targets)

    # Create visualizations
    print("\n=== Creating Visualizations ===")
    plot_eigenvalue_comparison(predictions, targets, file_name)
    plot_error_analysis(predictions, targets, file_name)

    return predictions, targets, per_eigenvalue_metrics, file_name


def main():
    """Main testing function with comprehensive analysis."""
    print("=== COMPREHENSIVE EIGENVALUE MODEL DIAGNOSIS ===")

    # Check if required files exist
    required_files = [MODEL_PATH, X_SCALER_PATH, Y_SCALER_PATH]
    missing_files = [f for f in required_files if not os.path.exists(f)]

    if missing_files:
        print(f"Error: Missing required files: {missing_files}")
        print("Please run train_eigenvalue_model.py first to train the model.")
        return

    # Check if data directory exists
    if not os.path.exists(DATA_DIR):
        print(f"Error: Data directory {DATA_DIR} not found!")
        return

    csv_files = glob.glob(os.path.join(DATA_DIR, "*.csv"))
    if not csv_files:
        print(f"Error: No CSV files found in {DATA_DIR}")
        return

    print(f"Found {len(csv_files)} CSV files for testing")
    print("All required files found. Starting comprehensive testing...")

    # Test random simulation with detailed analysis
    print("\n" + "=" * 60)
    print("DETAILED ANALYSIS OF ONE RANDOM SIMULATION")
    print("=" * 60)

    result = test_random_simulation()
    if result[0] is None:
        print("Testing failed!")
        return

    predictions, targets, per_eigenvalue_metrics, file_name = result

    # Summary of findings
    print("\n" + "=" * 60)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 60)

    r2_scores = [m['r2'] for m in per_eigenvalue_metrics]
    avg_r2 = np.mean(r2_scores)
    median_r2 = np.median(r2_scores)

    print(f"Average R² Score: {avg_r2:.4f}")
    print(f"Median R² Score: {median_r2:.4f}")

    if avg_r2 < 0.3:
        print("\n⚠️  MODEL PERFORMANCE IS POOR!")
        print("Possible issues:")
        print("1. Model architecture may be inadequate for eigenvalue prediction")
        print("2. Training data may not contain sufficient variability")
        print("3. Eigenvalue features may have high noise or complexity")
        print("4. Data normalization may not be appropriate")
        print("5. Model may be underfitting (need more complexity)")
    elif avg_r2 < 0.6:
        print("\n⚠️  MODEL PERFORMANCE IS MODERATE")
        print("Consider:")
        print("1. Increasing model complexity (more layers/neurons)")
        print("2. Adjusting input/output lag parameters")
        print("3. Feature engineering on input data")
    else:
        print("\n✅ MODEL PERFORMANCE IS GOOD!")

    print("\nCheck the generated PNG files for detailed visualizations:")
    print("- eigenvalue_comparison_*.png: Time series comparisons")
    print("- error_analysis_*.png: Comprehensive error analysis")


if __name__ == "__main__":
    main()