Current time: 2025-08-13 11:24:07
Current time: 2025-08-13 11:24:07
PyTorch version: 2.5.1+cu124
CUDA available: True
CUDA version: 12.4
GPU count: 1
GPU name: NVIDIA GeForce RTX 4060 Laptop GPU
Current GPU memory: 8.0 GB
Using device: cuda
Device type: cuda
GPU Memory: 0.00GB allocated, 0.00GB reserved
Traceback (most recent call last):
  File "D:\workspace\withPowerfactoryNew\adding_packages.py", line 75, in save_console_output
    func(*args, **kwargs)
  File "D:\workspace\withPowerfactoryNew\adding_packages.py", line 599, in main
    train_model(model, train_loader, criterion, optimizer, device)
  File "D:\workspace\withPowerfactoryNew\adding_packages.py", line 285, in train_model
    with autocast('cuda'):
  File "C:\Users\<USER>\.conda\envs\myenv_matlab\lib\site-packages\torch\cuda\amp\autocast_mode.py", line 41, in __enter__
    return super().__enter__()
  File "C:\Users\<USER>\.conda\envs\myenv_matlab\lib\site-packages\torch\amp\autocast_mode.py", line 353, in __enter__
    torch.set_autocast_enabled(self.device, self._enabled)
TypeError: set_autocast_enabled(): argument 'enabled' (position 2) must be bool, not str
