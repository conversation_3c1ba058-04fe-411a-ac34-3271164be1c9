import os
import numpy as np
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import pickle
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import glob
import pandas as pd

# === Configuration ===
MODEL_PATH = "FNN_NARX_eigenvalue_model.pth"
X_SCALER_PATH = "X_scaler.pkl"
Y_SCALER_PATH = "Y_scaler.pkl"
DATA_DIR = 'data/'

INPUT_LAG = 3
OUTPUT_LAG = 2
HIDDEN_SIZE = 128
OUTPUT_CHANNELS = 22

# Input and output columns (same as training)
INPUT_COLUMNS = list(range(2, 29))  # Columns 2-28 (27 features)
OUTPUT_COLUMNS = list(range(41, 63))  # Columns 41-62 (22 eigenvalue features)

# === Model Definition (same as training) ===
class FNN_NARX_Eigenvalue(nn.Module):
    """Feedforward Neural Network for NARX eigenvalue modeling."""
    def __init__(self, input_size, hidden_size, output_size):
        super(FNN_NARX_Eigenvalue, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, output_size)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.1)

    def forward(self, x, y):
        combined_input = torch.cat((x, y), dim=1)
        out = self.relu(self.fc1(combined_input))
        out = self.dropout(out)
        out = self.relu(self.fc2(out))
        out = self.dropout(out)
        out = self.fc3(out)
        return out

def load_model_and_scalers():
    """Load the trained model and scalers."""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Load scalers
    with open(X_SCALER_PATH, 'rb') as f:
        X_scaler = pickle.load(f)
    with open(Y_SCALER_PATH, 'rb') as f:
        Y_scaler = pickle.load(f)
    
    # Calculate input size
    input_size = INPUT_LAG * len(INPUT_COLUMNS) + OUTPUT_LAG * len(OUTPUT_COLUMNS)
    
    # Load model
    model = FNN_NARX_Eigenvalue(input_size, HIDDEN_SIZE, OUTPUT_CHANNELS).to(device)
    model.load_state_dict(torch.load(MODEL_PATH, map_location=device))
    model.eval()
    
    return model, X_scaler, Y_scaler, device

def load_test_data(file_path):
    """Load and preprocess a single test file."""
    data = np.loadtxt(file_path, delimiter=',', skiprows=1)
    X = data[:, INPUT_COLUMNS]
    Y = data[:, OUTPUT_COLUMNS]
    return X.astype(np.float32), Y.astype(np.float32)

def predict_sequence(model, X_scaled, Y_scaled, X_scaler, Y_scaler, device):
    """Predict eigenvalues for a sequence."""
    predictions = []
    targets = []
    
    num_timesteps = X_scaled.shape[0]
    
    with torch.no_grad():
        for t in range(max(INPUT_LAG, OUTPUT_LAG), num_timesteps):
            # Extract input sequences
            x_seq = X_scaled[t - INPUT_LAG:t, :].reshape(-1)
            y_seq = Y_scaled[t - OUTPUT_LAG:t, :].reshape(-1)
            target_y = Y_scaled[t, :]
            
            # Convert to tensors
            x_seq = torch.tensor(x_seq, dtype=torch.float32).unsqueeze(0).to(device)
            y_seq = torch.tensor(y_seq, dtype=torch.float32).unsqueeze(0).to(device)
            
            # Predict
            pred = model(x_seq, y_seq)
            
            predictions.append(pred.cpu().numpy().flatten())
            targets.append(target_y)
    
    predictions = np.array(predictions)
    targets = np.array(targets)
    
    # Denormalize predictions and targets
    predictions_denorm = Y_scaler.inverse_transform(predictions)
    targets_denorm = Y_scaler.inverse_transform(targets)
    
    return predictions_denorm, targets_denorm

def evaluate_predictions(predictions, targets):
    """Evaluate prediction quality."""
    mse = mean_squared_error(targets, predictions)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(targets, predictions)
    r2 = r2_score(targets, predictions)
    
    # Per-output metrics
    per_output_r2 = []
    per_output_rmse = []
    
    for i in range(predictions.shape[1]):
        r2_i = r2_score(targets[:, i], predictions[:, i])
        rmse_i = np.sqrt(mean_squared_error(targets[:, i], predictions[:, i]))
        per_output_r2.append(r2_i)
        per_output_rmse.append(rmse_i)
    
    return {
        'mse': mse,
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'per_output_r2': per_output_r2,
        'per_output_rmse': per_output_rmse
    }

def plot_predictions(predictions, targets, output_idx=0, max_points=500):
    """Plot predictions vs targets for a specific eigenvalue output."""
    # Limit points for better visualization
    if len(predictions) > max_points:
        step = len(predictions) // max_points
        pred_plot = predictions[::step, output_idx]
        target_plot = targets[::step, output_idx]
        time_plot = np.arange(0, len(predictions), step)
    else:
        pred_plot = predictions[:, output_idx]
        target_plot = targets[:, output_idx]
        time_plot = np.arange(len(predictions))
    
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    plt.plot(time_plot, target_plot, 'b-', label='True', alpha=0.7)
    plt.plot(time_plot, pred_plot, 'r--', label='Predicted', alpha=0.7)
    plt.xlabel('Time Step')
    plt.ylabel('Eigenvalue')
    plt.title(f'EV_{output_idx//2}_{"real" if output_idx%2==0 else "imag"} - Time Series')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.scatter(target_plot, pred_plot, alpha=0.6)
    plt.plot([target_plot.min(), target_plot.max()], [target_plot.min(), target_plot.max()], 'r--', lw=2)
    plt.xlabel('True Values')
    plt.ylabel('Predicted Values')
    plt.title(f'EV_{output_idx//2}_{"real" if output_idx%2==0 else "imag"} - Scatter Plot')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def plot_per_output_metrics(metrics):
    """Plot R² and RMSE for each eigenvalue output."""
    eigenvalue_labels = []
    for i in range(OUTPUT_CHANNELS):
        ev_num = i // 2
        part = "real" if i % 2 == 0 else "imag"
        eigenvalue_labels.append(f'EV_{ev_num}_{part}')
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # R² scores
    ax1.bar(range(OUTPUT_CHANNELS), metrics['per_output_r2'])
    ax1.set_xlabel('Eigenvalue Output')
    ax1.set_ylabel('R² Score')
    ax1.set_title('R² Score per Eigenvalue Output')
    ax1.set_xticks(range(OUTPUT_CHANNELS))
    ax1.set_xticklabels(eigenvalue_labels, rotation=45, ha='right')
    ax1.grid(True, alpha=0.3)
    
    # RMSE
    ax2.bar(range(OUTPUT_CHANNELS), metrics['per_output_rmse'])
    ax2.set_xlabel('Eigenvalue Output')
    ax2.set_ylabel('RMSE')
    ax2.set_title('RMSE per Eigenvalue Output')
    ax2.set_xticks(range(OUTPUT_CHANNELS))
    ax2.set_xticklabels(eigenvalue_labels, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def test_single_file(file_path):
    """Test the model on a single file."""
    print(f"\n=== Testing on {os.path.basename(file_path)} ===")
    
    # Load model and scalers
    model, X_scaler, Y_scaler, device = load_model_and_scalers()
    
    # Load test data
    X, Y = load_test_data(file_path)
    
    # Normalize data
    X_flat = X.reshape(-1, X.shape[-1])
    Y_flat = Y.reshape(-1, Y.shape[-1])
    
    X_scaled_flat = X_scaler.transform(X_flat)
    Y_scaled_flat = Y_scaler.transform(Y_flat)
    
    X_scaled = X_scaled_flat.reshape(X.shape)
    Y_scaled = Y_scaled_flat.reshape(Y.shape)
    
    # Make predictions
    predictions, targets = predict_sequence(model, X_scaled, Y_scaled, X_scaler, Y_scaler, device)
    
    # Evaluate
    metrics = evaluate_predictions(predictions, targets)
    
    print(f"Overall R² Score: {metrics['r2']:.4f}")
    print(f"Overall RMSE: {metrics['rmse']:.6f}")
    print(f"Overall MAE: {metrics['mae']:.6f}")
    
    # Show best and worst performing eigenvalues
    best_idx = np.argmax(metrics['per_output_r2'])
    worst_idx = np.argmin(metrics['per_output_r2'])
    
    print(f"Best performing eigenvalue: EV_{best_idx//2}_{'real' if best_idx%2==0 else 'imag'} (R²: {metrics['per_output_r2'][best_idx]:.4f})")
    print(f"Worst performing eigenvalue: EV_{worst_idx//2}_{'real' if worst_idx%2==0 else 'imag'} (R²: {metrics['per_output_r2'][worst_idx]:.4f})")
    
    return predictions, targets, metrics

def main():
    """Main testing function."""
    print("=== Eigenvalue Prediction Model Testing ===")
    
    # Check if model files exist
    if not os.path.exists(MODEL_PATH):
        print(f"Error: Model file {MODEL_PATH} not found!")
        return
    
    if not os.path.exists(X_SCALER_PATH) or not os.path.exists(Y_SCALER_PATH):
        print("Error: Scaler files not found!")
        return
    
    # Get test files
    csv_files = glob.glob(os.path.join(DATA_DIR, "*.csv"))
    if not csv_files:
        print(f"Error: No CSV files found in {DATA_DIR}")
        return
    
    # Test on the first file
    test_file = csv_files[0]
    predictions, targets, metrics = test_single_file(test_file)
    
    # Plot results
    print("\nGenerating plots...")
    plot_per_output_metrics(metrics)
    
    # Plot a few example eigenvalues
    for i in [0, 1, 10, 11]:  # Plot first real/imag pair and middle pair
        plot_predictions(predictions, targets, output_idx=i)
    
    print("\nTesting completed successfully!")
    print(f"Model shows good performance with overall R² = {metrics['r2']:.4f}")

if __name__ == "__main__":
    main()
